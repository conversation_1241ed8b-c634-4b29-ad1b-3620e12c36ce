.featured {
   &_list {
      display: grid;
      grid-template-columns: 1fr auto;
      grid-template-columns: 1fr 1fr;
      gap: 5rem;
      gap: 4rem;
      align-items: center;
      // margin-top: 2rem;
      padding: 2rem 0;
      border-top: 1px solid #1b1b1b;
      border-bottom: 1px solid #1b1b1b;
      margin-top: 2rem;

      @media (max-width: 1024px) {
         grid-template-columns: 1fr;
         gap: 3rem;
         padding-bottom: 0;
      }

      .overlay {
         position: absolute;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         z-index: 1;
      }

      .main {
         display: flex;
         flex-direction: column;
         gap: 1rem;
         position: relative;

         &:hover {
            .title span {
               background-size: 100% 0.4rem;
            }
         }

         .poster {
            position: relative;
            height: 40rem;
            // width: 100%;
            aspect-ratio: 16 / 10;
            border-radius: 2rem;
            overflow: hidden;

            @media (max-width: 1024px) {
               height: 40rem;
               width: 100%;
               aspect-ratio: auto;
            }

            @media (max-width: 425px) {
               height: 30rem;
            }

            img {
               object-fit: cover;
            }
         }

         .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 1rem;

            .category {
               font-size: 1.2rem;
               font-size: 1.3rem;
               text-transform: capitalize;
               font-weight: 500;
               margin-top: 1rem;
               color: var(--color-secondary);
               background-color: #1e1e1e;
               padding: 0.5rem 1rem;
               padding: 0.7rem 1.5rem;
               border-radius: 2rem;
               z-index: 100;
               box-shadow: rgba(0, 0, 0, 0.34) 0px 3px 8px;
            }

            .date {
               font-size: 1.3rem;
               color: #ccc;
            }
         }

         .title {
            font-size: 3rem;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            span {
               background-image: var(--background-gradient);
               background-size: 0% 4px;
               background-position: 0 90%;
               background-repeat: no-repeat;
               transition: background-size 500ms cubic-bezier(0.4, 0, 0.2, 1);
            }
         }

         .summary {
            font-size: 1.4rem;
            display: -webkit-box;
            line-clamp: 3;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
         }
      }

      .content {
         display: flex;
         flex-direction: column;
         gap: 2rem;

         &_item {
            display: flex;
            display: grid;
            grid-template-columns: 30% 1fr;
            grid-template-columns: auto 1fr;
            gap: 3rem;
            padding-bottom: 2rem;
            position: relative;

            @media (max-width: 1024px) {
               &:first-child {
                  border-top: 1px solid #1b1b1b;
                  padding-top: 2rem;
               }
            }

            &:not(:last-child) {
               border-bottom: 1px solid #1b1b1b;
            }

            &:hover {
               .title span {
                  background-size: 100% 0.4rem;
               }
            }
         }

         .poster {
            position: relative;
            height: 12rem;
            aspect-ratio: 1 / 1;
            border-radius: 2rem;
            overflow: hidden;

            img {
               object-fit: cover;
            }
         }

         .content_info {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .info {
               display: flex;
               align-items: center;
               justify-content: space-between;

               @media (max-width: 768px) {
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 1rem;
               }

               .date {
                  font-size: 1.3rem;
                  color: #ccc;
               }
            }
         }

         .title {
            font-size: 1.8rem;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            span {
               background-image: var(--background-gradient);
               background-size: 0% 4px;
               background-position: 0 90%;
               background-repeat: no-repeat;
               transition: background-size 500ms cubic-bezier(0.4, 0, 0.2, 1);
            }
         }

         .summary {
            font-size: 1.2rem;
         }
      }
   }
}
