.controls {
   display: flex;
   padding-left: 0;
   padding-right: 0;
   padding-top: 0.75rem;
   padding-bottom: 0.75rem;
   gap: 0.75rem;
   align-items: center;

   @media (min-width: 768px) {
      padding-left: 0.25rem;
      padding-right: 0.25rem;
      padding-top: 1.25rem;
      padding-bottom: 1.25rem;
   }

   @media (max-width: 768px) {
      padding-top: 2rem;
   }

   &_btn {
      display: flex;
      background: transparent;
      // border: 1px solid #fdfdfd5f;
      border-radius: 9999px;
      font-size: 3rem;

      background: rgba($color: #000, $alpha: 0.25);
      backdrop-filter: blur(16px) saturate(180%);
   }
}
