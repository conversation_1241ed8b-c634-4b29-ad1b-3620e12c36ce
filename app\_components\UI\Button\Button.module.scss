.button {
   padding: 0.8rem 3rem;
   font-size: 1.4rem;
   border-radius: 10rem;
   font-weight: 500;
   display: flex;
   align-items: center;
   gap: 1rem;

   svg {
      font-size: 2rem;
   }

   &.play {
      padding: 1.4rem 2.8rem;
      line-height: 1;
   }

   &.normal {
      justify-content: center;
   }

   &.small {
      padding: 0.8rem 2rem;
      font-size: 1.3rem;
   }

   &.medium {
      padding: 1rem 2rem;
      font-size: 1.4rem;

      @media (max-width: 768px) {
         padding: 0.8rem 2rem;
         font-size: 1.3rem;
      }
   }

   &.primary {
      border: 1.5px solid transparent;
      background: var(--background-gradient);
      background-size: 200% 100%;
      background-position: 100%;
      background: #fff;
      color: #000;
   }

   &.secondary {
      background: transparent;
      border: 1.5px solid var(--text-secondary);
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(16px) saturate(180%);
      background-color: rgba(0, 0, 0, 0.25);
   }

   &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
   }
}
