.dropdown {
   position: relative;

   &_menu {
      position: absolute;
      top: calc(100% + 10px);
      right: 0;
      width: 200px;
      background-color: #111;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      z-index: 1000;

      &_item {
         padding: 12px 16px;
         font-size: 1.4rem;
         color: var(--text-secondary);
         display: flex;
         align-items: center;
         gap: 10px;
         cursor: pointer;
         transition: all 0.2s ease;

         &:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
         }

         &.logout {
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            color: #ff5252;

            &:hover {
               border-top: 1px solid transparent;
            }

            .loader_icon {
               color: #ff5252;
               display: flex;
               align-items: center;
               justify-content: center;
               font-size: 1.8rem;
            }
         }

         &.logging_out {
            pointer-events: none;
            opacity: 0.8;
            cursor: not-allowed;
         }

         svg {
            font-size: 1.8rem;
         }
      }
   }
}
