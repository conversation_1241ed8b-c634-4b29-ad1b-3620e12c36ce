.slider_card {
   position: relative;
   border-radius: 2rem;
   height: 35rem;
   box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
   display: flex;
   // align-items: flex-end;

   flex-direction: column;
   padding: 1rem;
   backdrop-filter: blur(16px) saturate(180%);
   -webkit-backdrop-filter: blur(16px) saturate(180%);
   background-color: rgba(0, 0, 0, 0.05);

   @media (max-width: 1024px) {
      min-width: 20rem;
      height: 30rem;
   }

   @media (max-width: 768px) {
      height: 20rem;
      width: 20rem;
   }

   .image {
      // position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      // min-width: 40rem;
      // padding: 1rem;
      border-radius: 1.5rem;
      object-fit: cover;
      filter: brightness(75%);
   }

   .content {
      display: flex;
      flex-direction: column;
      // position: absolute;
      // position: relative;
      z-index: 10;
      // padding: 1rem;
      // align-items: flex-end;
      // height: 100%;

      text-align: center;

      .line {
         position: absolute;
         top: 0;
         margin-bottom: 0.5rem;
         border-radius: 9999px;
         width: 0.75rem;
         background: var(--background-gradient);
         background-size: 200% 100%;
         background-position: 100%;
         height: 2px;
      }

      .tag {
         font-size: 1rem;
         text-transform: capitalize;
         line-height: 1.5rem;
         color: var(--text-secondary);
      }

      .title {
         font-size: 1.4rem;
         line-height: 1.5rem;
         color: var(--text-primary);
         padding-top: 1rem;
      }
   }
}
