.overview {
   display: flex;
   flex-direction: column;
   gap: 2rem;

   .section {
      background: #121212;
      border-radius: 16px;
      padding: 2rem;

      .section_title {
         font-size: 1.8rem;
         font-weight: 600;
         margin-bottom: 1.5rem;
         color: var(--text-primary);
      }

      .bio {
         font-size: 1.45rem;
         line-height: 1.6;
         color: var(--text-secondary);
      }

      .empty_state {
         font-size: 1.45rem;
         line-height: 1.6;
         color: #666;
         font-style: italic;
      }

      .personal_info {
         display: grid;
         grid-template-columns: repeat(2, 1fr);
         gap: 1.5rem;

         @media (max-width: 768px) {
            grid-template-columns: 1fr;
         }

         .info_item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            .label {
               font-size: 1.3rem;
               color: #a7a7a7;
            }

            .value {
               font-size: 1.5rem;
               color: var(--text-primary);
            }
         }
      }

      .social_links {
         display: flex;
         gap: 1.5rem;
         flex-wrap: wrap;

         .social_icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            background-color: #222;
            color: var(--text-primary);
            font-size: 1.8rem;
            transition: all 0.2s ease;

            &:hover {
               transform: translateY(-3px);
               background-color: #333;
            }
         }
      }
   }
}
