.header {
   position: relative;
   width: 100%;

   .cover {
      position: relative;
      width: 100%;
      height: 200px;
      overflow: hidden;
      border-radius: 16px;

      .cover_image {
         object-fit: cover;
      }

      .cover_gradient {
         width: 100%;
         height: 100%;
         background: linear-gradient(135deg, #6366f1, #8b5cf6, #d946ef);
      }
   }

   .profile_info {
      display: flex;
      align-items: flex-end;
      margin-top: -60px;
      padding: 0 2rem;
      position: relative;
      z-index: 100;

      @media (max-width: 768px) {
         flex-direction: column;
         align-items: center;
         text-align: center;
      }

      .edit_button_container {
         margin-left: auto;
         padding-bottom: 1rem;
         display: flex;
         gap: 1rem;

         @media (max-width: 768px) {
            flex-direction: column;
            align-items: center;
            margin-left: 0;
            margin-top: 1.5rem;
            width: 100%;
         }

         > button,
         a {
            width: fit-content;
         }
      }

      .avatar_container {
         margin-right: 2rem;

         :global(.avatar) {
            border: 4px solid var(--background);
         }

         @media (max-width: 768px) {
            margin-right: 0;
            margin-bottom: 1.5rem;
         }
      }

      .details {
         padding-bottom: 1rem;
         align-self: center;

         .name_container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
         }

         .name {
            font-size: 2.4rem;
            font-weight: 700;
         }

         .private_badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 1.2rem;
            font-weight: 500;
         }

         .location,
         .occupation {
            font-size: 1.4rem;
            color: var(--text-secondary);
            margin-bottom: 0.3rem;
         }
      }
   }
}
