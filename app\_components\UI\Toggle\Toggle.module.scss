.toggle_group {
   display: flex;
   justify-content: space-between;
   align-items: center;
   margin-bottom: 1.6rem;
   padding: 1.6rem;
   background-color: #1a1a1a;
   border-radius: 0.8rem;
   border: 1px solid #333;
}

.toggle_label_container {
   flex: 1;
}

.toggle_label {
   font-size: 1.5rem;
   font-weight: 500;
   color: #fff;
   margin-bottom: 0.4rem;
}

.toggle_description {
   font-size: 1.3rem;
   color: #999;
   margin: 0;
}

.toggle_switch {
   position: relative;
   display: inline-block;
   width: 4.8rem;
   height: 2.4rem;
}

.toggle_input {
   opacity: 0;
   width: 0;
   height: 0;

   &:checked + .toggle_slider {
      background-color: #fff;
   }

   &:checked + .toggle_slider:before {
      transform: translateX(2.4rem);
      background-color: #000;
   }

   &:focus + .toggle_slider {
      box-shadow: 0 0 1px #fff;
   }
}

.toggle_slider {
   position: absolute;
   cursor: pointer;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   background-color: #333;
   transition: 0.4s;
   border-radius: 2.4rem;

   &:before {
      position: absolute;
      content: "";
      height: 1.8rem;
      width: 1.8rem;
      left: 0.3rem;
      bottom: 0.3rem;
      background-color: #fff;
      transition: 0.4s;
      border-radius: 50%;
   }
}
