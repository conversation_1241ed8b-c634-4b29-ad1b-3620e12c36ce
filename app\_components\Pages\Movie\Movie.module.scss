.movie {
   display: flex;
   flex-direction: column;
   margin: 0 auto;
   margin-top: 4rem;
   margin-top: 6rem;
   width: 100%;
   max-width: 1200px;
   max-width: 1400px;
   min-height: calc(100vh - 18rem);
   margin-bottom: 4rem;
   padding: 0 6rem;

   @media (max-width: 1024px) {
      padding: 0 3rem;
   }

   @media (max-width: 768px) {
      margin-top: 3rem;
   }

   @media (max-width: 425px) {
      padding: 0 2rem;
   }

   .background_image {
      filter: blur(30px) brightness(35%);
      object-fit: cover;
      z-index: -1;
   }

   &_info {
      display: flex;
      display: grid;
      grid-template-columns: 35% 1fr;
      grid-template-columns: 378px 1fr;
      gap: 6rem;

      @media (max-width: 1024px) {
         grid-template-columns: 40% 1fr;
      }

      @media (max-width: 768px) {
         grid-template-columns: 1fr;
         grid-template-rows: auto 1fr;
         gap: 2rem;
      }

      &_poster {
         position: relative;
         width: 100%;
         height: 50rem;
         object-fit: cover;
         border-radius: 2.5rem;
         overflow: hidden;

         background: #131313;
         border: 1rem solid #131313;

         .pulse_wrapper {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;

            .pulse {
               transform: translate(-50%, -50%);
               background: rgba(0, 0, 0, 0.25);
               backdrop-filter: blur(16px);
               height: 60px;
               padding: 2rem 2.5rem;
               border-radius: 5rem;
               font-size: 1.6rem;
               gap: 1rem;
               box-shadow: 0 0 0 0 rgba(225, 225, 225, 0.5);
               transform: scale(1);
               animation: pulse 2s infinite;
               display: flex;
               align-items: center;
               justify-content: center;
               cursor: pointer;

               svg {
                  font-size: 2.5rem;
               }
            }
         }

         img {
            object-fit: cover;
            filter: brightness(90%);
            border-radius: 1.8rem;
         }
      }

      &_content {
         display: flex;
         flex-direction: column;
         gap: 2rem;

         p {
            display: flex;
            align-items: center;
            gap: 1rem;
            line-height: 1.2;

            svg {
               font-size: 2.5rem;
            }
         }

         .back {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;

            &:hover {
               text-decoration: underline;

               span {
                  transform: translateX(-0.3rem);
               }
            }

            span {
               padding: 1rem;
               border-radius: 50%;
               background-color: #1b1b1b;
               display: flex;
               transition: all 0.2s;
            }

            svg {
               font-size: 1.6rem;
            }
         }

         .title {
            font-size: 4.5rem;
            line-height: 1;
         }

         .description {
            font-size: 1.6rem;
            line-height: 1.75;
            color: #eeeeee;
         }

         .text {
            font-size: 1.4rem;
            font-size: 1.5rem;
            font-weight: 500;
            color: var(--text-secondary);
         }

         .year {
            margin-top: 0.5rem;
            font-size: 1.6rem;
            color: #666;
         }

         .genres {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;

            .genre {
               font-size: 1.5rem;
               background-color: #1b1b1b;
               padding: 0.8rem 2rem;
               border-radius: 5rem;
               text-transform: capitalize;
            }
         }

         .btns {
            display: flex;
            gap: 1.5rem;

            @media (max-width: 425px) {
               flex-direction: column;
               justify-content: center;

               button {
                  display: flex;
                  justify-content: center;
               }
            }
         }
      }
   }

   // &_comments {
   //    margin-top: 8rem;

   //    .title {
   //       font-size: 2.5rem;
   //       text-align: center;
   //       margin-bottom: 2rem;
   //    }

   //    .comments {
   //       width: 900px;
   //       margin: 0 auto;

   //       .comment {
   //          display: flex;
   //          display: grid;
   //          grid-template-columns: 4rem 1fr;
   //          gap: 1.5rem;

   //          background-color: #141414;
   //          padding: 2rem;
   //          border-radius: 2rem;

   //          &_img {
   //             width: 4rem;
   //             height: 4rem;
   //             background-color: var(--text-secondary);
   //             border-radius: 9999px;
   //          }

   //          &_info {
   //             display: flex;
   //             flex-direction: column;
   //             gap: 1rem;

   //             &_title {
   //                font-size: 1.6rem;
   //                line-height: 1.2;
   //             }

   //             &_time {
   //                font-size: 1.2rem;
   //                font-weight: 500;
   //                color: #999;
   //             }

   //             &_text {
   //                font-size: 1.3rem;
   //             }

   //             &_btns {
   //                display: flex;
   //                gap: 2rem;
   //             }

   //             &_btn {
   //                display: flex;
   //                align-items: center;
   //                gap: 0.5rem;
   //                font-size: 1.4rem;
   //                line-height: 1;
   //                color: var(--text-primary);
   //                cursor: pointer;

   //                svg {
   //                   font-size: 2rem;
   //                }
   //             }
   //          }
   //       }
   //    }
   // }
}

@keyframes pulse {
   0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(225, 225, 225, 0.2);
   }

   70% {
      transform: scale(1);
      box-shadow: 0 0 0 15px rgba(222, 84, 72, 0);
   }

   100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(222, 84, 72, 0);
   }
}
