.verify_email {
   display: flex;
   justify-content: center;
   align-items: center;
   min-height: calc(80svh - 4rem);
   padding: 2rem;

   &_card {
      background-color: #080808;
      border-radius: 1.5rem;
      overflow: hidden;
      width: 100%;
      max-width: 45rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      position: relative;

      &::before {
         content: "";
         position: absolute;
         top: 0;
         left: 0;
         right: 0;
         height: 0.5rem;
         background: var(--background-gradient);
         z-index: 1;
      }

      &_header {
         padding: 3rem;
         text-align: center;

         &_title {
            font-size: 2.4rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
         }

         &_subtitle {
            font-size: 1.6rem;
            color: var(--text-secondary);
         }
      }

      &_content {
         padding: 2rem 3rem 3rem;
         display: flex;
         flex-direction: column;
         align-items: center;
         justify-content: center;
         min-height: 20rem;
      }

      &_footer {
         padding: 2rem 3rem;
         display: flex;
         justify-content: center;
         border-top: 1px solid #1f1f1f;

         button {
            background-color: transparent;
            color: var(--text-primary);
            font-size: 1.6rem;
            padding: 1rem 2rem;
            border-radius: 10rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:hover {
               background-color: rgba(255, 255, 255, 0.05);
               border-color: rgba(255, 255, 255, 0.3);
            }

            a {
               display: flex;
               align-items: center;
               gap: 1rem;
            }
         }
      }
   }
}

.state {
   display: flex;
   flex-direction: column;
   align-items: center;
   text-align: center;
   width: 100%;

   &_icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 10rem;
      height: 10rem;
      border-radius: 50%;
      margin-bottom: 2rem;
      font-size: 4rem;

      &.loading {
         background-color: rgba(255, 255, 255, 0.1);
         color: var(--text-primary);
      }

      &.success {
         background-color: rgba(0, 200, 83, 0.15);
         color: #00c853;
      }

      &.error {
         background-color: rgba(255, 82, 82, 0.15);
         color: #ff5252;
      }
   }

   &_title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
   }

   &_message {
      font-size: 1.6rem;
      color: var(--text-secondary);
      max-width: 40rem;
   }
}

@keyframes pulse {
   0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.2);
   }

   70% {
      transform: scale(1);
      box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
   }

   100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
   }
}

.pulse {
   animation: pulse 2s infinite;
}

@media (max-width: 768px) {
   .verify_email {
      padding: 1rem;

      &_card {
         max-width: 40rem;

         &_header {
            padding: 2.5rem 2rem 1rem;

            &_title {
               font-size: 2.2rem;
            }
         }

         &_content {
            padding: 1.5rem 2rem 2.5rem;
         }

         &_footer {
            padding: 1.5rem 2rem;
         }
      }
   }

   .state {
      &_icon {
         width: 8rem;
         height: 8rem;
         font-size: 3.5rem;
      }

      &_title {
         font-size: 1.8rem;
      }

      &_message {
         font-size: 1.4rem;
      }
   }
}

@media (max-width: 425px) {
   .verify_email {
      &_card {
         max-width: 30rem;
      }
   }
}
