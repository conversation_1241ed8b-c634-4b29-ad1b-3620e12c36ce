"use client";

import { useEffect, useRef } from "react";

export function useOutsideClick<T extends HTMLElement = HTMLDivElement>(
   handler: () => void
) {
   const ref = useRef<T | null>(null);
   const listenCapturing = true;

   useEffect(() => {
      function handleClick(e: MouseEvent) {
         if (ref.current && !ref.current.contains(e.target as Node)) {
            handler();
         }
      }

      document.addEventListener("click", handleClick, listenCapturing);

      return () =>
         document.removeEventListener("click", handleClick, listenCapturing);
   }, [handler, listenCapturing]);

   return ref;
}
