.user_profile {
   width: 100%;
   margin: 0 auto;
   overflow: hidden;
   position: relative;
}

.header {
   position: relative;
   width: 100%;

   .cover {
      position: relative;
      width: 100%;
      height: 240px;
      border-radius: 1.6rem;
      overflow: hidden;

      .cover_image {
         object-fit: cover;
      }

      .cover_gradient {
         width: 100%;
         height: 100%;
         background: linear-gradient(135deg, #6366f1, #8b5cf6, #d946ef);
      }
   }

   .profile_info {
      display: flex;
      padding: 0 2rem;
      margin-top: -60px;
      position: relative;
      z-index: 10;
      align-items: flex-end;
      padding-bottom: 2rem;

      @media (max-width: 768px) {
         flex-direction: column;
         align-items: center;
         text-align: center;
      }

      .avatar_container {
         margin-right: 2rem;

         :global(.avatar) {
            border: 4px solid #0a0a0a;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
         }

         @media (max-width: 768px) {
            margin-right: 0;
            margin-bottom: 1.5rem;
         }
      }

      .user_details {
         flex: 1;
         padding-bottom: 0.5rem;
         transform: translateY(-10px);

         .name_container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;

            @media (max-width: 768px) {
               justify-content: center;
            }

            .name {
               font-size: 2.6rem;
               font-weight: 700;
               margin-bottom: 0;
               line-height: 1.4;
            }

            .private_badge {
               display: flex;
               align-items: center;
               gap: 0.5rem;
               background-color: rgba(0, 0, 0, 0.5);
               background-color: #181818;
               color: #fff;
               padding: 0.4rem 0.8rem;
               border-radius: 4px;
               font-size: 1.2rem;
               font-weight: 500;
            }
         }

         .meta_info {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-top: 0.5rem;

            @media (max-width: 768px) {
               justify-content: center;
            }

            .meta_item {
               display: flex;
               align-items: center;
               color: #ccc;
               font-size: 1.4rem;

               svg {
                  margin-right: 0.5rem;
                  color: #999;
               }

               a {
                  color: #ccc;
                  text-decoration: none;
                  transition: color 0.2s;

                  &:hover {
                     color: #fff;
                     text-decoration: underline;
                  }
               }

               &.masked {
                  .masked_content {
                     width: 100px;
                     height: 1.4rem;
                     background-color: #151515;
                     border-radius: 4px;
                  }
               }
            }
         }
      }
   }
}

.content_selector {
   display: flex;
   gap: 1rem;
   padding: 0 2rem;
   border-bottom: 1px solid rgba(255, 255, 255, 0.1);

   .selector_button {
      background: none;
      border: none;
      color: #999;
      font-size: 1.6rem;
      font-weight: 500;
      padding: 1.5rem 2rem;
      cursor: pointer;
      position: relative;
      transition: color 0.2s;

      &:hover {
         color: #fff;
      }

      &.active {
         color: #fff;

         &:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: #fff;
            border-radius: 3px 3px 0 0;
         }
      }
   }
}

.content {
   padding: 2rem 0;

   .overview_section {
      .bio_section {
         margin-bottom: 3rem;

         .section_title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #fff;
         }

         .bio {
            font-size: 1.5rem;
            line-height: 1.6;
            color: #ccc;
            white-space: pre-line;
         }
      }
   }

   .media_section {
      .no_media {
         display: flex;
         justify-content: center;
         align-items: center;
         height: 200px;
         background-color: rgba(255, 255, 255, 0.05);
         border-radius: 8px;

         p {
            color: #999;
            font-size: 1.6rem;
         }
      }

      // Custom styling for media items in public profile
      :global(.media_grid) {
         gap: 2rem;

         :global(.media_item) {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transition:
               transform 0.3s ease,
               box-shadow 0.3s ease;

            &:hover {
               transform: translateY(-5px);
               box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            }

            :global(.media_title) {
               padding: 1.2rem;
               background: linear-gradient(
                  to bottom,
                  rgba(0, 0, 0, 0) 0%,
                  rgba(0, 0, 0, 0.8) 100%
               );
            }
         }
      }
   }

   .private_content {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 300px;
      background-color: #121212;
      border-radius: 16px;

      .private_message {
         text-align: center;
         padding: 2rem;
         max-width: 400px;

         .lock_icon {
            font-size: 3rem;
            color: #fff;
            margin-bottom: 1.5rem;
         }

         p {
            font-size: 1.6rem;
            color: #fff;
            line-height: 1.5;
         }
      }
   }
}
