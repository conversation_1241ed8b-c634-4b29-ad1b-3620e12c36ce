.like_button {
   display: flex;
   align-items: center;
   gap: 0.8rem;
   background: transparent;
   border: none;
   color: var(--text-secondary);
   font-weight: 500;
   margin: 0.8rem 1.8rem;
   padding: 0 !important;
   border-radius: 2rem;
   cursor: pointer;
   transition: all 0.2s ease;
   position: relative;
   user-select: none;
   color: #ff4757 !important;

   &:hover {
      color: var(--text-primary);
      background-color: rgba(255, 255, 255, 0.05);
   }

   &:active {
      transform: scale(0.98);
   }

   &.liked {
      color: #ff4757;

      &:hover {
         color: #ff3742;
      }

      .heart_filled {
         color: #ff4757;
      }
   }

   &.loading {
      cursor: not-allowed;
      opacity: 0.7;
   }

   &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
   }

   .heart_container {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
   }

   .heart_filled,
   .heart_outline {
      font-size: 1.8rem;
      transition: all 0.2s ease;
   }

   .heart_filled {
      color: #ff4757;
   }

   .heart_outline {
      color: inherit;
   }

   .like_count {
      font-size: 1.5rem;
      font-weight: 500;
      min-width: 1.5rem;
      text-align: left;
   }

   .loading_spinner {
      position: absolute;
      top: 50%;
      right: 0.5rem;
      transform: translateY(-50%);
      width: 1.2rem;
      height: 1.2rem;
      border: 2px solid transparent;
      border-top: 2px solid var(--text-secondary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
   }

   // Size variations
   &.small {
      padding: 0.6rem 1.2rem;
      gap: 0.6rem;
      font-size: 1.3rem;

      .heart_filled,
      .heart_outline {
         font-size: 1.4rem;
      }

      .like_count {
         font-size: 1.3rem;
      }

      .loading_spinner {
         width: 1rem;
         height: 1rem;
      }
   }

   &.medium {
      padding: 0.8rem 1.8rem;
      gap: 0.8rem;
      font-size: 1.5rem;

      .heart_filled,
      .heart_outline {
         font-size: 1.8rem;
      }

      .like_count {
         font-size: 1.5rem;
      }

      .loading_spinner {
         width: 1.2rem;
         height: 1.2rem;
      }
   }

   &.large {
      padding: 1rem 2.2rem;
      gap: 1rem;
      font-size: 1.7rem;

      .heart_filled,
      .heart_outline {
         font-size: 2.2rem;
      }

      .like_count {
         font-size: 1.7rem;
      }

      .loading_spinner {
         width: 1.4rem;
         height: 1.4rem;
      }
   }
}

@keyframes spin {
   0% {
      transform: translateY(-50%) rotate(0deg);
   }
   100% {
      transform: translateY(-50%) rotate(360deg);
   }
}

// Hover effects for different states
.like_button:not(.liked):hover {
   .heart_outline {
      color: #ff4757;
      transform: scale(1.05);
   }
}

.like_button.liked:hover {
   .heart_filled {
      transform: scale(1.05);
      filter: brightness(1.1);
   }
}

// Focus styles for accessibility
.like_button:focus-visible {
   outline: 2px solid #ff4757;
   outline-offset: 2px;
}

// Animation for heart when liked
.like_button.liked .heart_container {
   animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
   0% {
      transform: scale(1);
   }
   25% {
      transform: scale(1.1);
   }
   50% {
      transform: scale(1);
   }
   75% {
      transform: scale(1.05);
   }
   100% {
      transform: scale(1);
   }
}

// Responsive design
@media (max-width: 768px) {
   .like_button {
      padding: 0.6rem 1.4rem;
      gap: 0.6rem;

      &.small {
         padding: 0.5rem 1rem;
         gap: 0.5rem;
      }

      &.large {
         padding: 0.8rem 1.8rem;
         gap: 0.8rem;
      }
   }
}

@media (max-width: 480px) {
   .like_button {
      padding: 0.5rem 1.2rem;
      gap: 0.5rem;
      font-size: 1.4rem;

      .heart_filled,
      .heart_outline {
         font-size: 1.6rem;
      }

      .like_count {
         font-size: 1.4rem;
      }
   }
}
