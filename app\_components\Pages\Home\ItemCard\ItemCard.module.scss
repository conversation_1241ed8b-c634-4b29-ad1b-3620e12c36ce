.item {
   // margin: 2.5rem 0;
   margin: 1.5rem 0;
   // margin-bottom: 2.5rem;
   display: grid;
   align-items: center;
   gap: 2rem;
   position: relative;

   &:hover {
      img {
         transform: scale(1.05);
      }

      .info .info_title span {
         background-size: 100% 0.4rem;
      }
   }

   .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
   }

   &.horizontal {
      flex-direction: row;
      grid-template-columns: auto 1fr;

      &:not(:last-child) {
         margin-bottom: 3rem;
      }
   }

   .poster {
      position: relative;
      height: 18rem;
      width: 32.5rem;
      border-radius: 1rem;
      overflow: hidden;

      @media (max-width: 1024px) {
         width: 35rem;
      }

      @media (max-width: 768px) {
         width: auto;
         height: 13rem;
         aspect-ratio: 1 / 1;
         min-width: 15rem;
      }

      @media (max-width: 425px) {
         height: 13rem;
         aspect-ratio: 1 / 1;
         min-width: 13rem;
      }

      img {
         object-fit: cover;

         transition: 0.3s;
      }

      .position {
         position: absolute;
         top: 1rem;
         left: 1rem;
         font-size: 1.8rem;
         font-weight: 500;
         color: #000;
         background: #fff;
         background: #1e1e1e;
         min-width: 3.5rem;
         min-height: 3.5rem;
         border-radius: 1rem;
         display: flex;
         justify-content: center;
         align-items: center;
         z-index: 10;
         color: #fff;
      }
   }

   .info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      &_date {
         font-size: 1.2rem;
         font-size: 1.3rem;
         font-weight: 500;
         color: #a8a8a8;

         @media (max-width: 768px) {
            font-size: 1.1rem;
         }
      }

      &_title {
         color: var(--color-primary);
         font-size: 1.8rem;
         font-size: 2rem;
         font-weight: 500;
         line-height: 1.5;
         display: -webkit-box;
         line-clamp: 2;
         -webkit-line-clamp: 2;
         -webkit-box-orient: vertical;
         overflow: hidden;

         @media (max-width: 768px) {
            font-size: 1.6rem;
            line-clamp: 3;
            -webkit-line-clamp: 3;
         }

         @media (max-width: 425px) {
            font-size: 1.4rem;
         }

         span {
            display: inline;
            background-image: var(--background-gradient);
            background-size: 0% 4px;
            background-position: 0 90%;
            background-repeat: no-repeat;
            transition-property: background-size;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 500ms;
         }
      }

      &_tags {
         display: flex;
         gap: 1rem;
         font-size: 1.3rem;
         font-size: 1.4rem;
         color: var(--text-secondary);
      }

      &_summary {
         font-size: 1.2rem;
         font-size: 1.3rem;
         color: var(--text-secondary);
         display: -webkit-box;
         line-clamp: 2;
         -webkit-line-clamp: 2;
         -webkit-box-orient: vertical;
         overflow: hidden;

         @media (max-width: 768px) {
            display: none;
         }
      }

      &_category {
         margin-top: 1rem;
      }
   }
}
