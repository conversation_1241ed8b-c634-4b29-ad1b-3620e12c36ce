.creative_spotlight {
   position: relative;
   width: 100%;
   overflow: hidden;
   box-shadow:
      rgba(0, 0, 0, 0.25) 0px 14px 28px,
      rgba(0, 0, 0, 0.22) 0px 10px 10px;
   background-color: #111111;
   padding: 4rem;
   border-radius: 3rem;

   @media (max-width: 768px) {
      padding: 2.5rem;
   }

   @media (max-width: 435px) {
      padding: 2rem;
      border-radius: 2rem;
   }

   .container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      min-height: 35rem;
      position: relative;

      @media (max-width: 768px) {
         grid-template-columns: 1fr;
         min-height: auto;
      }
   }

   .content {
      padding-right: 2rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      z-index: 2;

      @media (max-width: 768px) {
         padding: 0;
         padding-top: 2.5rem;
         order: 2;
      }

      .content_header {
         display: flex;
         align-items: center;
         gap: 3rem;

         .title {
            font-size: 2rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 1.2rem;
            cursor: auto;
         }
      }
   }

   .categories {
      display: flex;
      gap: 1rem;
      margin-bottom: 1.5rem;
   }

   .content_info {
      &:hover {
         .title span {
            background-size: 100% 0.4rem;
         }
      }

      .title {
         font-size: 3rem;
         font-weight: 700;
         margin-bottom: 1rem;
         color: #fff;
         display: -webkit-box;
         line-clamp: 2;
         -webkit-line-clamp: 2;
         -webkit-box-orient: vertical;
         overflow: hidden;

         span {
            display: inline;
            background-image: var(--background-gradient);
            background-size: 0% 4px;
            background-position: 0 90%;
            background-repeat: no-repeat;
            transition-property: background-size;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 500ms;
         }

         @media (max-width: 768px) {
            font-size: 2rem;
         }
      }

      .description {
         font-size: 1.6rem;
         line-height: 1.6;
         color: rgba(255, 255, 255, 0.8);
         margin-bottom: 1.5rem;
         max-width: 90%;
         display: -webkit-box;
         line-clamp: 3;
         -webkit-line-clamp: 3;
         -webkit-box-orient: vertical;
         overflow: hidden;

         @media (max-width: 768px) {
            max-width: 100%;
         }
      }
   }

   .image_container {
      position: relative;
      height: 41rem;
      border-radius: 1.6rem;
      overflow: hidden;

      @media (max-width: 768px) {
         height: 30rem;
         order: 1;
      }

      @media (max-width: 425px) {
         height: 25rem;
      }

      img {
         object-fit: cover;
      }
   }

   .navigation {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      margin-top: auto;
   }

   .nav_button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 4rem;
      height: 4rem;
      border-radius: 50%;
      background-color: #191919;
      color: #fff;
      font-size: 2rem;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
         background-color: #292929;
      }
   }

   .preview_section {
      margin-top: 2rem;
   }

   .progress_container {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      margin-top: 1.5rem;
   }

   .progress_bar {
      flex: 1;
      height: 4px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;
      position: relative;
   }

   .progress_fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: var(--background-gradient);
      border-radius: 2px;
   }

   .progress_text {
      font-size: 1.4rem;
      color: rgba(255, 255, 255, 0.6);
   }
}
