.image_edit {
   display: flex;
   justify-content: center;
   width: 100%;

   .image_container {
      position: relative;
      cursor: pointer;
      width: 120px;
      height: 120px;

      &:hover .edit_overlay {
         opacity: 1;
      }

      &.profile_container {
         border-radius: 50%;
      }

      &.cover_container {
         width: 100%;
         height: 200px;
         border-radius: 16px;
         overflow: hidden;
      }

      .profile_image {
         position: relative;
         width: 120px;
         height: 120px;
         border-radius: 50%;
         overflow: hidden;

         :global(.avatar) {
            width: 100% !important;
            height: 100% !important;
            border: 4px solid var(--background);
         }
      }

      .cover_image {
         position: relative;
         width: 100%;
         height: 100%;

         .image {
            object-fit: cover;
         }

         .cover_gradient {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #d946ef);
         }
      }

      .edit_overlay {
         position: absolute;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         background-color: rgba(0, 0, 0, 0.5);
         display: flex;
         justify-content: center;
         align-items: center;
         opacity: 0;
         transition: opacity 0.3s ease;
         color: white;
         z-index: 10;
      }
   }
}

.dialog_content {
   display: flex;
   flex-direction: column;
   gap: 2rem;

   .preview_container {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;

      .avatar_preview {
         width: 150px;
         height: 150px;
         border-radius: 50%;
         overflow: hidden;

         .preview_image {
            width: 100%;
            height: 100%;
            object-fit: cover;
         }
      }

      .cover_preview {
         position: relative;
         width: 100%;
         height: 200px;
         border-radius: 16px;
         overflow: hidden;

         .preview_image {
            object-fit: cover;
         }

         .cover_placeholder {
            width: 100%;
            height: 100%;
            background-color: #171717;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            color: var(--text-secondary);
         }
      }
   }

   .upload_controls {
      display: flex;
      flex-direction: column;
      width: 100%;
      gap: 1rem;
      margin-bottom: 1rem;
   }

   .dialog_actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: -16px;

      button {
         min-width: 100px;
      }
   }
}

.spinner {
   animation: spin 1s linear infinite;
   margin-right: 0.5rem;
}

@keyframes spin {
   from {
      transform: rotate(0deg);
   }
   to {
      transform: rotate(360deg);
   }
}
