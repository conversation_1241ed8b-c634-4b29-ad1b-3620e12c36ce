.dialog_overlay {
   position: fixed;
   inset: 0;
   background-color: rgba(0, 0, 0, 0.5);
   display: flex;
   align-items: center;
   justify-content: center;
   z-index: 1000;

   @media (max-width: 768px) {
      align-items: flex-start;
   }

   &.prevent_fullscreen {
      align-items: center;
   }
}

.blur_overlay {
   backdrop-filter: blur(4px);
}

.dialog_content {
   position: relative;
   background-color: #111;
   border-radius: 12px;
   padding: 24px;
   padding: 30px;
   width: 450px;
   max-width: 90vw;
   max-height: 95svh;
   box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
   overflow: hidden;
   overflow-y: auto;

   @media (max-width: 768px) {
      max-width: 100vw;
      width: 100%;
      height: 100%;
      max-height: 100vh;
      border-radius: 0;
      padding: 24px 20px;
      display: flex;
      flex-direction: column;
   }

   &.prevent_fullscreen {
      @media (max-width: 768px) {
         max-width: 90vw;
         width: 450px;
         height: auto;
         max-height: 80vh;
         border-radius: 12px;
      }
   }
}

.dialog_close {
   position: absolute;
   top: 12px;
   right: 12px;
   background: transparent;
   border: none;
   color: #999;
   cursor: pointer;
   font-size: 20px;
   padding: 4px;
   display: flex;
   align-items: center;
   justify-content: center;
   transition: color 0.2s;
   z-index: 10;

   @media (max-width: 768px) {
      top: 16px;
      right: 16px;
      font-size: 24px;
   }

   &:hover {
      color: white;
   }
}

.dialog_header {
   display: flex;
   flex-direction: column;
   align-items: center;
   margin-bottom: 24px;
}

.dialog_title {
   font-size: 24px;
   font-weight: 600;
   color: white;
   margin-top: 8px;
   margin-bottom: 8px;
   text-align: center;
}

.dialog_description {
   font-size: 14px;
   color: #999;
   text-align: center;
   margin-bottom: 8px;
}

.dialog_footer {
   margin-top: 24px;
   display: flex;
   flex-direction: column;
   gap: 16px;

   @media (max-width: 768px) {
      margin-top: auto;
      padding-bottom: 16px;
   }
}

.dialog_divider {
   display: flex;
   align-items: center;
   margin: 16px 0;
   color: #666;
   font-size: 14px;

   &::before,
   &::after {
      content: "";
      flex: 1;
      height: 1px;
      background-color: #333;
   }

   &::before {
      margin-right: 8px;
   }

   &::after {
      margin-left: 8px;
   }
}

.dialog_logo {
   display: flex;
   align-items: center;
   justify-content: center;
   margin-bottom: 8px;
   padding: 4px;

   &.rounded {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #222;

      svg,
      img {
         width: 40px;
         height: 40px;
      }
   }
}
