.animated_gradient_text {
   position: relative;
   display: flex;
   max-width: fit-content;
   flex-direction: row;
   align-items: center;
   justify-content: center;
   // border-radius: 5rem;
   font-weight: 500;
   backdrop-filter: blur(10px);
   transition: box-shadow 0.5s ease-out;
   overflow: hidden;
   cursor: pointer;
}

.gradient_overlay {
   position: absolute;
   top: 1px;
   left: 0;
   right: 0;
   bottom: 0;
   background-size: 300% 100%;
   animation: gradient linear infinite;
   border-radius: inherit;
   z-index: 0;
   pointer-events: none;

   &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      border-radius: inherit;
      width: calc(100% - 3px);
      height: calc(100% - 3px);
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-color: #060606;
      z-index: -1;
   }
}

@keyframes gradient {
   0% {
      background-position: 0 0;
   }
   50% {
      background-position: 400% 0;
   }
   100% {
      background-position: 0 0;
   }
}

.text_content {
   display: inline-block;
   position: relative;
   z-index: 2;
   background-size: 300% 100%;
   background-clip: text;
   -webkit-background-clip: text;
   color: transparent;
   animation: gradient linear infinite;
}
