.latest_posts {
   display: flex;
   flex-direction: column;
   width: 100%;
   margin-bottom: 4rem;
}

.posts_list {
   width: 100%;
}

.loading_state {
   display: flex;
   flex-direction: column;
   gap: 1.5rem;
   width: 100%;
   margin-top: 2rem;
}

.skeleton_card {
   display: grid;
   grid-template-columns: auto 1fr;
   gap: 2rem;
   align-items: center;
   margin-bottom: 1.5rem;

   @media (max-width: 768px) {
      grid-template-columns: 35% 1fr;
   }

   @media (max-width: 425px) {
      grid-template-columns: auto 1fr;
   }
}

.skeleton_poster {
   position: relative;
   height: 20rem;
   aspect-ratio: 16 / 9;
   border-radius: 1rem;
   overflow: hidden;

   @media (max-width: 1024px) {
      height: 15rem;
   }

   @media (max-width: 768px) {
      height: 15rem;
      width: 100%;
      aspect-ratio: auto;
   }

   @media (max-width: 425px) {
      height: 13rem;
      aspect-ratio: 1 / 1;
      min-width: 13rem;
   }
}

.skeleton_info {
   display: flex;
   flex-direction: column;
   gap: 1rem;
   width: 100%;
}

.skeleton_category {
   margin-top: 0.5rem;
}

.see_more {
   justify-self: center;
   display: flex;
   justify-content: center;
   align-items: center;
   gap: 0.8rem;
   border-radius: 30px;
   padding: 1.2rem 3rem;
   background-color: #1a1a1a;
   border: 1px solid #2a2a2a;
   color: var(--text-primary);
   margin: 2rem auto;
   margin-bottom: 0;
   font-size: 1.5rem;
   font-weight: 500;
   min-width: 17.5rem;
   min-height: 5rem;
   transition: all 0.25s ease;
   position: relative;
   cursor: pointer;
   overflow: hidden;

   &_content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.8rem;

      .see_more_icon {
         font-size: 1.8rem;
         transition: transform 0.3s ease;
         animation: pulse 2s infinite;
      }
   }

   &:disabled {
      cursor: not-allowed;
      opacity: 0.8;
   }

   &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
         to right,
         transparent,
         rgba(255, 255, 255, 0.05),
         transparent
      );
      animation: shimmer 2s infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
   }

   &:hover:not(:disabled)::before {
      opacity: 1;
   }

   &::after {
      content: "";
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 1px;
      background: linear-gradient(
         to right,
         transparent,
         rgba(255, 255, 255, 0.2),
         transparent
      );
      opacity: 0;
      transition: opacity 0.3s ease;
   }

   &:hover:not(:disabled) {
      background-color: #222;
      transform: translateY(-2px);

      &::after {
         opacity: 1;
      }
   }

   &:active:not(:disabled) {
      transform: translateY(0);
   }

   .button_content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.8rem;
      width: 100%;

      span {
         font-weight: 500;
      }

      svg {
         font-size: 1.8rem;
         transition: transform 0.3s ease;
         animation: pulse 2s infinite;
      }
   }

   .loading_container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
   }

   .spinner {
      font-size: 2rem;
      animation: spin 1s linear infinite;
   }

   @keyframes pulse {
      0%,
      100% {
         transform: translateY(0);
      }
      50% {
         transform: translateY(2px);
      }
   }

   @keyframes spin {
      0% {
         transform: rotate(0deg);
      }
      100% {
         transform: rotate(360deg);
      }
   }
}

@keyframes shimmer {
   0% {
      left: -100%;
   }
   100% {
      left: 100%;
   }
}
