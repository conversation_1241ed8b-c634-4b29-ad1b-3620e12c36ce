.post {
   display: flex;
   flex-direction: column;
   max-width: 1200px;
   width: 100%;
   margin: 0 auto;
   padding: 0 2rem;

   .header {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1.5rem;
      margin: 1rem 0;

      @media (max-width: 435px) {
         flex-direction: column;
      }

      .metadata {
         display: flex;
         align-items: center;
         gap: 1.5rem;
      }

      .date {
         font-size: 1.3rem;
         font-weight: 500;
         line-height: 1;
         color: var(--text-secondary);
      }

      .modified {
         font-size: 1.3rem;
         font-weight: 500;
         line-height: 1;
         color: var(--text-secondary);
         display: flex;
         align-items: center;
         gap: 0.5rem;

         span {
            display: inline-flex;
            align-items: center;
         }
      }

      .reading_time {
         font-size: 1.3rem;
         font-weight: 500;
         line-height: 1;
         color: var(--text-secondary);
         display: flex;
         align-items: center;
         gap: 0.5rem;

         svg {
            font-size: 1.6rem;
         }
      }

      .view_count {
         font-size: 1.3rem;
         font-weight: 500;
         line-height: 1;
         color: var(--text-secondary);
         display: flex;
         align-items: center;
         gap: 0.5rem;

         svg {
            font-size: 1.6rem;
         }
      }
   }

   // Title styling
   .title {
      font-size: 5rem;
      font-weight: 600;
      text-align: center;
      padding: 0 6rem;
      margin-bottom: 2.5rem;
      margin-top: 2rem;
      line-height: 1.2;

      @media (max-width: 1024px) {
         padding: 0 3rem;
         font-size: 4rem;
      }

      @media (max-width: 768px) {
         padding: 0 2rem;
         font-size: 3.2rem;
      }

      @media (max-width: 425px) {
         padding: 0;
         font-size: 2.8rem;
      }
   }

   // Featured image
   .poster {
      position: relative;
      width: 100%;
      aspect-ratio: 16 / 9;
      margin: 0 auto 3rem;
      border-radius: 1.2rem;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

      img {
         object-fit: cover;
         object-position: center 20%;
      }
   }

   // Summary text
   .summary {
      font-size: 1.6rem;
      line-height: 1.6;
      color: var(--text-primary);
      margin: 0 auto 3rem;
      text-align: justify;
      font-weight: 500;
      font-style: italic;
   }

   .post_layout {
      max-width: 800px;
      margin: 0 auto;
   }

   // Divider
   .divider {
      width: 100%;
      height: 1px;
      background-color: #292929;
      margin: 3rem 0;
   }

   // Main content
   .content {
      font-size: 1.7rem;
      line-height: 1.8;
      color: var(--text-secondary);

      p {
         margin-bottom: 2.5rem;
      }

      h2 {
         font-size: 3rem;
         font-weight: 600;
         color: var(--text-primary);
         margin: 4rem 0 2rem;
         scroll-margin-top: 10rem;
      }

      h3 {
         font-size: 2.4rem;
         font-weight: 600;
         color: var(--text-primary);
         margin: 2rem 0 1.5rem;
         scroll-margin-top: 10rem;
      }

      ul,
      ol {
         margin: 2rem 0;
         padding-left: 2.5rem;

         li {
            margin-bottom: 1rem;
         }
      }

      blockquote {
         border-left: 4px solid #444;
         padding-left: 2rem;
         margin: 3rem 0;
         font-style: italic;
         color: #bbb;
      }

      a {
         color: #3a86ff;
         text-decoration: none;
         transition: color 0.2s;

         &:hover {
            text-decoration: underline;
         }
      }

      img {
         max-width: 100%;
         width: 100%;
         height: auto;
         border-radius: 0.8rem;
         margin: 1rem 0;
         object-fit: cover;
      }

      code {
         background-color: #181818;
         padding: 0.2rem 0.5rem;
         border-radius: 0.3rem;
         font-family: monospace;
         font-size: 1.5rem;
      }

      pre {
         background-color: #181818;
         padding: 2rem;
         border-radius: 0.8rem;
         overflow-x: auto;
         margin: 3rem 0;

         code {
            background-color: transparent;
            padding: 0;
         }
      }

      .image {
         position: relative;
         height: 50rem;
         width: 100%;
         margin: 3rem 0;
         border-radius: 0.8rem;
         overflow: hidden;

         @media (max-width: 768px) {
            height: 30rem;
         }

         img {
            object-fit: cover;
            object-position: center 20%;
            margin: 0;
         }
      }
   }
}
