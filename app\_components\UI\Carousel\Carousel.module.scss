.container {
   position: absolute;
   z-index: 20;
   height: 100%;
   width: 100%;
   margin-top: -5rem;
   overflow: hidden;

   .content {
      display: grid;
      grid-template-columns: 50% 50%;
      align-items: center;
      width: 100%;
      height: 100%;

      @media (max-width: 768px) {
         grid-template-columns: 100%;
         grid-template-rows: 1.5fr 1fr;
         padding-bottom: 7rem;
      }

      .slider_left {
         display: flex;
         align-items: center;
         padding-left: 6rem;
         padding-right: 3rem;
         height: 100%;

         @media (max-width: 1024px) {
            padding-left: 4rem;
            margin-bottom: -10rem;
         }
      }

      .slider_right {
         display: flex;
         padding: 1rem;
         flex-direction: column;
         flex: 1 1 0%;
         justify-content: flex-start;

         @media (max-width: 1024px) {
            margin-left: 2rem;
         }

         @media (max-width: 768px) {
            padding-top: 0;
         }

         .slides {
            display: grid;
            width: 100%;
            gap: 1.5rem;

            @media (max-width: 1024px) {
               gap: 0.5rem;
            }
         }
      }
   }
}

.background_image,
.background_image_current {
   position: absolute;
   left: 0;
   top: 0;
   height: 100%;
   width: 100%;
   object-fit: cover;
   // filter: brightness(40%);
   // filter: brightness(80%);
}

.background_image {
   z-index: 10;
}

.background {
   &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
         to right,
         rgba(0, 0, 0, 1) 0%,
         rgba(0, 0, 0, 0.5) 70%,
         rgba(255, 255, 255, 0) 100%
      );
      background-image: radial-gradient(
         circle at 100% 50%,
         rgba(255, 255, 255, 0) 10%,
         // rgba(255, 255, 255, 0) 53%,
         #000000 100%
      );
      background: radial-gradient(
         circle at 100% 50%,
         rgba(0, 0, 0, 0),
         rgba(0, 0, 0, 0.2),
         rgba(0, 0, 0, 0.4),
         rgba(0, 0, 0, 0.6),
         rgba(0, 0, 0, 0.8),
         rgba(0, 0, 0, 0.95),
         #000000
      );
      z-index: 10;
   }
}
