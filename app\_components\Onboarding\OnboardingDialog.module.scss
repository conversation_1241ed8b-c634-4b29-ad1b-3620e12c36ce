.onboarding {
   &_content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 0 1rem;
   }

   &_icon {
      display: flex;
      font-size: 5rem;
      color: var(--text-primary);

      &_container {
         display: flex;
         justify-content: center;
         align-items: center;
         width: 10rem;
         height: 10rem;
         border-radius: 50%;
         background: rgba(255, 255, 255, 0.05);
         margin-bottom: 2rem;
      }
   }

   &_title {
      font-size: 2.4rem;
      font-weight: 600;
      margin-bottom: 1.6rem;
      color: var(--text-primary);
   }

   &_description {
      font-size: 1.6rem;
      line-height: 1.6;
      color: var(--text-secondary);
      margin-bottom: 1rem;
   }

   &_footer {
      display: flex;
      justify-content: space-between;
      width: 100%;
   }

   &_progress {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 3rem;
      gap: 0.8rem;

      &_dot {
         width: 1rem;
         height: 1rem;
         border-radius: 50%;
         background-color: rgba(255, 255, 255, 0.2);
         transition: background-color 0.3s ease;

         &.active {
            background: #ff0000;
         }
      }

      &_text {
         font-size: 1.4rem;
         color: var(--text-secondary);
         margin-left: 1rem;
      }
   }

   &_divider {
      display: flex;
      align-items: center;
      margin: 2rem 0;
      width: 100%;
      color: #666;
      font-size: 1.4rem;

      &::before,
      &::after {
         content: "";
         flex: 1;
         height: 1px;
         background-color: #333;
      }

      &::before {
         margin-right: 1rem;
      }

      &::after {
         margin-left: 1rem;
      }
   }

   &_buttons {
      display: flex;
      gap: 1rem;

      button {
         width: auto;
      }

      .skip_button {
         background: transparent;
         border: none;
         color: var(--text-secondary);
         font-size: 1.4rem;
         cursor: pointer;
         padding: 1rem;
         transition: color 0.2s;

         &:hover {
            color: var(--text-primary);
         }
      }
   }
}
