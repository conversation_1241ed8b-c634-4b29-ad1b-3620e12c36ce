.loading_dots {
   display: flex;
   justify-content: center;
   align-items: center;

   span {
      animation: loadingDots 1.4s infinite ease-in-out;
      background-color: var(--text-secondary);
      border-radius: 50%;
      display: inline-block;
      height: 0.6rem;
      margin: 0 0.3rem;
      width: 0.6rem;

      &:nth-child(1) {
         animation-delay: 0s;
      }

      &:nth-child(2) {
         animation-delay: 0.2s;
      }

      &:nth-child(3) {
         animation-delay: 0.4s;
      }
   }
}

@keyframes loadingDots {
   0%,
   80%,
   100% {
      transform: scale(0.6);
      opacity: 0.5;
   }
   40% {
      transform: scale(1);
      opacity: 1;
   }
}
