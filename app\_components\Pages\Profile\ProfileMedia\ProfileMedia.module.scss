.media {
   .title {
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
   }

   .grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;

      @media (max-width: 1024px) {
         grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 768px) {
         grid-template-columns: 1fr;
      }

      .media_item {
         background-color: #151515;
         border-radius: 12px;
         overflow: hidden;
         transition: transform 0.2s ease;

         &:hover {
            transform: translateY(-5px);

            .image {
               transform: scale(1.05);
            }
         }

         .image_container {
            position: relative;
            width: 100%;
            height: 200px;
            overflow: hidden;

            .image {
               object-fit: cover;
               transition: transform 0.3s ease;
            }
         }

         .info {
            padding: 1.5rem;

            .media_title {
               font-size: 1.6rem;
               font-weight: 500;
               margin-bottom: 0.5rem;
            }

            .date {
               font-size: 1.2rem;
               color: var(--text-secondary);
            }
         }
      }
   }

   .empty {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      background-color: #151515;
      border-radius: 12px;
      font-size: 1.6rem;
      color: var(--text-secondary);
   }

   .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      font-size: 1.6rem;
      color: var(--text-secondary);
      gap: 1rem;

      .spinner {
         animation: spin 1s linear infinite;
      }
   }
}

@keyframes spin {
   from {
      transform: rotate(0deg);
   }
   to {
      transform: rotate(360deg);
   }
}
