:root {
   --background: #030303;
   --foreground: #ededed;

   --background-gradient: linear-gradient(to right, red 50%, #f5eb0c);
   --background-gradient-2: linear-gradient(
      to right,
      red 20%,
      #f5eb0c 40%,
      red 60%,
      #f5eb0c 80%,
      red
   );

   --color-dark: #171717;
   --color-light: #f2f2f2;

   --text-primary: #fff;
   --text-secondary: #d5d5d6;
   // --text-secondary: #ededed;
}

*,
*::before,
*::after {
   margin: 0;
   padding: 0;
   box-sizing: border-box;
}

html {
   font-size: 62.5%;

   @media (max-width: 1024px) {
      font-size: 55%;
   }
}

html,
body {
   max-width: 100vw;
}

body {
   color: var(--foreground);
   background: var(--background);
   -webkit-font-smoothing: antialiased;
   -moz-osx-font-smoothing: grayscale;
   min-height: 100vh;

   display: flex;
   flex-direction: column;
   overflow-x: hidden;
}

input,
textarea {
   font-family: inherit;
}

a {
   color: inherit;
   text-decoration: none;
}

button {
   cursor: pointer;
   padding: 0.5rem;
   outline: none;
   border: none;
   font-family: inherit;
   color: var(--text-secondary);
}

/* Add global scrollbar styling for the entire application */
* {
   scrollbar-width: thin; /* Firefox */
   scrollbar-color: rgba(255, 255, 255, 0.2) rgba(0, 0, 0, 0.1); /* Firefox */

   /* WebKit/Blink browsers */
   &::-webkit-scrollbar {
      width: 6px;
   }

   &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
   }

   &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      transition: background-color 0.2s ease;

      &:hover {
         background-color: rgba(255, 255, 255, 0.3);
      }
   }
}

/* Custom styling for Sonner toast component */

/* Toast container */
[data-sonner-toaster] {
   font-family: inherit !important;
   --front-color: var(--text-primary) !important;
   --normal-bg: #171717 !important;
   --normal-border: rgba(255, 255, 255, 0.1) !important;
   --error-bg: #2d0607 !important;
   --error-border: #4d0408 !important;
   --success-bg: #001f0f !important;
   --success-border: #003d1c !important;
}

/* Base toast styling */
[data-sonner-toast] {
   background: #171717 !important;
   border-radius: 12px !important;
   color: var(--text-primary) !important;
}

/* Toast title styling */
[data-sonner-toast] [data-title] {
   font-weight: 600 !important;
   font-size: 1.5rem !important;
   margin-bottom: 4px !important;
}

/* Toast description styling */
[data-sonner-toast] [data-description] {
   font-size: 1.3rem !important;
   color: var(--text-secondary) !important;
   line-height: 1.4 !important;
}

/* Toast icon styling */
[data-sonner-toast] [data-icon] {
   display: flex !important;
   align-items: center !important;
   margin-right: 10px !important;
   font-size: 1.8rem !important;
}

/* Success toast styling */
[data-sonner-toast][data-type="success"] {
   background: #001f0f !important;
   border-color: #003d1c !important;
   color: #59f3a6 !important;
}

[data-sonner-toast][data-type="success"] [data-description] {
   color: #59f3a6 !important;
}

[data-sonner-toast][data-type="success"] [data-icon] {
   color: #59f3a6 !important;
}

/* Error toast styling */
[data-sonner-toast][data-type="error"] {
   background: #2d0607 !important;
   border-color: #4d0408 !important;
   color: #ff9ea1 !important;
}

[data-sonner-toast][data-type="error"] [data-description] {
   color: #ff9ea1 !important;
}

[data-sonner-toast][data-type="error"] [data-icon] {
   color: #ff9ea1 !important;
}

[data-sonner-toast].custom-toast button {
   color: #000 !important;
   background-color: #fff !important;
}
