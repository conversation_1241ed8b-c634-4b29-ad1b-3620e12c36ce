@import url("https://fonts.googleapis.com/css2?family=Zen+Dots&display=swap");

.item {
   display: grid;
   grid-template-columns: 40% 1fr;
   display: flex;
   justify-content: center;
   gap: 3.5rem;
   // max-width: 120rem;
   margin: 1rem auto;
   padding: 1.3rem;
   border-radius: 2rem;
   background-color: #131313;
   position: relative;

   display: grid;
   grid-template-columns: auto 1fr;
   width: 100%;

   @media (max-width: 1024px) {
      gap: 2.5rem;
   }

   @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: 1.5fr 1fr;
      gap: 2rem;
   }

   .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;

      &:hover ~ .image img {
         transform: scale(1.05);
      }
   }

   .position {
      font-size: 4rem;
      font-weight: 600;
   }

   .image {
      position: relative;
      border-radius: 1.5rem;
      overflow: hidden;
      height: 20rem;
      width: 35rem;

      @media (max-width: 1024px) {
         width: 30rem;
      }

      @media (max-width: 768px) {
         width: 20rem;
         width: 100%;
         height: 100%;
      }

      img {
         object-fit: cover;
         filter: brightness(80%);
         transition: transform 0.4s;
      }

      .position {
         position: absolute;
         top: 1rem;
         // top: 3rem;
         left: 1rem;
         // left: 3rem;
         padding: 0.5rem 1.5rem;
         border-radius: 2rem;
         font-size: 1.2rem;
         font-weight: 600;
         background: #fff;
         color: #000;
         z-index: 10;
      }

      .play {
         font-size: 2rem;
         position: absolute;
         top: 50%;
         left: 50%;
         transform: translate(-50%, -50%);
         padding: 1.5rem;
         display: flex;
         border-radius: 10rem;
         background: rgba(255, 255, 255, 0.25);
         backdrop-filter: blur(16px);
         z-index: 10;
      }
   }

   .info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 0.5rem;
      padding: 2rem 0.5rem;
      padding-right: 1.5rem;
      color: var(--text-primary);
      // max-width: 60rem;
      // width: 70%;
      // width: 60%;

      &_category {
         padding: 0.2rem 0.8rem;
         transform: translateX(-2px);
         // background-color: #fff;
         // color: #000;
         font-size: 1.2rem;
         font-weight: 600;
         font-weight: 500;
         letter-spacing: 1px;
         border-radius: 2rem;
         align-self: flex-start;
         background-color: rgb(41, 41, 41);
         background-color: #2e2e2e;
      }

      &_author {
         font-size: 1.4rem;
         padding: 1rem 0;
         color: var(--text-secondary);
      }

      &_title {
         font-size: 3rem;

         @media (max-width: 1024px) {
            font-size: 2.5rem;
         }

         @media (max-width: 600px) {
            font-size: 2rem;
         }
      }

      &_text {
         font-size: 1.4rem;
         font-weight: 600;
         color: var(--text-secondary);
         color: #a8a8a8;
      }

      &_description {
         font-size: 1.5rem;
         margin-top: 0rem;
         display: -webkit-box;
         line-clamp: 2;
         -webkit-line-clamp: 2;
         -webkit-box-orient: vertical;
         overflow: hidden;

         @media (max-width: 1024px) {
            font-size: 1.4rem;
            margin-top: 0.5rem;
         }
      }

      &_time {
         font-size: 1.4rem;
         color: var(--text-secondary);
         padding: 1rem 0;
      }
   }
}
