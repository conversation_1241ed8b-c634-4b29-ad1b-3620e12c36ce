"use client";

import Image from "next/image";
import { useMemo, useState } from "react";
import styles from "./UserAvatar.module.scss";

type AvatarSize = "small" | "medium" | "large" | "xlarge";

interface UserAvatarProps {
   name?: string | null;
   image?: string | null;
   onClick?: () => void;
   darkBackground?: boolean;
   size?: AvatarSize;
}

export default function UserAvatar({
   name,
   image,
   onClick,
   darkBackground = false,
   size = "medium",
}: UserAvatarProps) {
   const [imageError, setImageError] = useState(false);
   const initials = useMemo(() => {
      if (!name) return "U";

      // Get first two letters of the name
      const nameParts = name.trim().split(" ");
      if (nameParts.length === 1) {
         return name.substring(0, 2).toUpperCase();
      }

      // Get first letter of first name and first letter of last name
      return (
         nameParts[0][0] + nameParts[nameParts.length - 1][0]
      ).toUpperCase();
   }, [name]);

   return (
      <div
         className={`${styles.avatar} ${styles[size]} ${
            darkBackground ? styles.dark_bg : ""
         }`}
         onClick={onClick}
      >
         {image && !imageError ? (
            <Image
               src={image}
               alt={name || "User"}
               width={
                  size === "small"
                     ? 40
                     : size === "medium"
                       ? 80
                       : size === "large"
                         ? 120
                         : 150
               }
               height={
                  size === "small"
                     ? 40
                     : size === "medium"
                       ? 80
                       : size === "large"
                         ? 120
                         : 150
               }
               className={styles.image}
               onError={() => setImageError(true)}
            />
         ) : (
            initials
         )}
      </div>
   );
}
