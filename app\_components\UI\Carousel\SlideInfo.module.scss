.slide_info {
   display: flex;
   flex-direction: column;
   gap: 1.5rem;
   gap: 3rem;
   position: relative;

   @media (max-width: 768px) {
      gap: 2rem;
   }

   .divider {
      position: absolute;
      top: -1rem;
      left: 0.5rem;
      border-radius: 2rem;
      width: 2.5rem;
      height: 4px;
      background: var(--background-gradient);
      background-size: 200% 100%;
      background-position: 100%;
   }

   &_content {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      @media (max-width: 768px) {
         gap: 1rem;
      }

      .category {
         padding: 0.2rem 0.8rem;
         transform: translateX(-2px);
         font-size: 1.2rem;
         font-weight: 500;
         letter-spacing: 1px;
         border-radius: 2rem;
         align-self: flex-start;
         background-color: #2e2e2e;
      }

      .title {
         color: var(--text-primary);
         margin-top: 0.25rem;
         margin-bottom: 0.25rem;
         font-size: 6rem;
         margin-top: 0.75rem;
         margin-bottom: 0.75rem;
         font-size: 6rem;
         line-height: 1;
         font-weight: 600;

         @media (max-width: 1024px) {
            font-size: 5rem;
         }

         @media (max-width: 768px) {
            font-size: 4rem;
         }

         @media (max-width: 425px) {
            font-size: 3rem;
         }

         // @media (min-width: 768px) {
         //    margin-top: 0.75rem;
         //    margin-bottom: 0.75rem;
         //    font-size: 6rem;
         //    line-height: 1;
         // }
      }

      .description {
         font-size: 1.4rem;
         line-height: 1.75;
         font-weight: 500;
         color: #eeeeee;
         max-width: 53rem;

         display: -webkit-box;
         line-clamp: 3;
         -webkit-line-clamp: 3;
         -webkit-box-orient: vertical;
         overflow: hidden;

         @media (max-width: 768px) {
            font-size: 1.3rem;
         }

         @media (max-width: 425px) {
            font-size: 1.2rem;
         }
      }
   }

   &_btns {
      display: flex;
      gap: 1.5rem;

      @media (max-width: 425px) {
         flex-direction: column;
      }

      button {
         padding: 1.2rem 2.4rem;
         padding: 1.4rem 2.8rem;
         border-radius: 10rem;
         font-weight: 500;
         display: flex;
         align-items: center;
         gap: 1rem;

         @media (max-width: 1024px) {
            padding: 1.2rem 2rem;
         }

         @media (max-width: 768px) {
            padding: 1rem 1.5rem;
            font-size: 1.2rem;
         }

         a {
            display: flex;
            align-items: center;
            gap: 1rem;
         }

         svg {
            font-size: 2rem;

            @media (max-width: 768px) {
               font-size: 1.5rem;
            }
         }

         &.btn_primary {
            background: var(--background-gradient);
            background-size: 200% 100%;
            background-position: 100%;
            background: #fff;
            color: #000;
            border: 1.5px solid #fff;
         }

         &.btn_secondary {
            background: transparent;
            border: 1.5px solid var(--text-secondary);
            backdrop-filter: blur(6px);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(0, 0, 0, 0.25);
         }
      }
   }
}
