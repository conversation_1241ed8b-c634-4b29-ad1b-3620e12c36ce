.container {
   position: fixed;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   display: flex;
   justify-content: center;
   align-items: center;
   height: 100%;
   width: 100%;
   z-index: 1001;

   &.hidden {
      display: none;
   }
}

.modal {
   position: relative;
   border-radius: 1.5rem;
   background-color: var(--bg-modal);
   z-index: 1000;
   box-shadow: rgba(0, 0, 0, 0.5) 0px 10px 20px -10px;
   border: none;
   width: 80%;
   height: 80%;
   background-color: #181818;
   padding: 1rem;
   border-radius: 3rem;

   @media (max-width: 768px) {
      width: 95%;
      height: 95%;
   }

   @media (max-width: 480px) {
      width: 100%;
      height: 100%;
      border-radius: 0;
      padding: 1.5rem 1rem;
   }

   > * {
      border-radius: 2rem;

      @media (max-width: 480px) {
         border-radius: 0;
      }
   }
}

.overlay {
   position: fixed;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   background-color: rgba(0, 0, 0, 0.5);
   z-index: 100;
}

.close__btn {
   position: absolute;
   top: 1.5rem;
   right: 1.5rem;
   line-height: 1;
   font-size: 2.2rem;
   color: var(--text-secondary);
   background-color: #181818;
   cursor: pointer;
   padding: 1.2rem;
   display: flex;
   align-items: center;
   border-radius: 20rem;
   border: none;
   transition: all 0.3s;

   @media (max-width: 450px) {
      font-size: 2rem;
      top: 1.5rem;
      right: 1.5rem;
   }
}
