.skeleton {
   background-color: #333;
   border-radius: 4px;
   position: relative;
   overflow: hidden;

   &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
         90deg,
         transparent,
         rgba(255, 255, 255, 0.1),
         transparent
      );
      animation: shimmer 2s infinite ease-in-out;
   }
}

@keyframes shimmer {
   0% {
      transform: translateX(-100%);
   }
   100% {
      transform: translateX(100%);
   }
}

.text {
   height: 1.6rem;
   width: 100%;
   margin-bottom: 0.8rem;
}

.circle {
   border-radius: 50%;
   height: 4rem;
   width: 4rem;
}

.rect {
   width: 10rem;
   height: 10rem;
}

.skeleton_comment {
   padding: 1.6rem;
   border-radius: 8px;
   width: 100%;
}

.comment_header {
   display: flex;
   align-items: center;
   margin-bottom: 0.8rem;
   gap: 1.2rem;
}

.comment_meta {
   display: flex;
   flex-direction: column;
   gap: 0.4rem;
}

.comment_body {
   margin-left: 5.2rem;
   margin-bottom: 0.5rem;
   display: flex;
   flex-direction: column;
   gap: 0.6rem;
}

.comment_actions {
   margin-left: 5.2rem;
   display: flex;
   gap: 1.2rem;
}
