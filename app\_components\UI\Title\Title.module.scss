.title {
   font-size: 4rem;
   font-weight: 500;
   transform: skewX(-10deg);
   width: fit-content;
   margin: 0 auto;
   margin-bottom: 1rem;
   cursor: default;

   @media (max-width: 1024px) {
      font-size: 3rem;
   }

   @media (max-width: 768px) {
      font-size: 2.5rem;
   }

   &::after {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--background-gradient);
      margin-top: 0.5rem;
      background-image: var(--background-gradient);
      background-repeat: no-repeat;
      background-size: 100% 1.5rem;
      background-position: 0 90%;
      transition: background-size 0.15s linear;
      z-index: -1;

      @media (max-width: 1024px) {
         background-size: 100% 1rem !important;
         background-position: 0 80%;
      }
   }

   &:hover {
      &::after {
         background-size: 100% 3rem !important;
      }
   }

   &.secondary {
      font-weight: 600;
      font-size: 2.2rem;
      margin: 0;
      transform: skewX(0deg);
      color: var(--text-secondary);

      &::after {
         background-position: 0 75%;
         background-size: 50% 0.3rem;
      }

      &:hover {
         &::after {
            background-size: 100% 1rem !important;
         }
      }
   }
}
