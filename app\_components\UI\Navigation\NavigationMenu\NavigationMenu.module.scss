.nav_menu {
   position: relative;
   margin-left: 2rem;

   @media (max-width: 850px) {
      margin-left: auto;
   }

   @media (min-width: 850px) {
      display: none;
   }

   &_controls {
      display: flex;
      align-items: center;
      gap: 1rem;
   }

   .avatar_container {
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease-in-out;
   }

   .menu {
      position: absolute;
      top: calc(100% + 1.5rem);
      right: -1rem;
      background-color: #0f0f0f;
      padding: 0;
      width: calc(100vw - 4rem);
      max-width: 45rem;
      border-radius: 0.8rem;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
      overflow: hidden;

      @media (max-width: 425px) {
         width: calc(100vw - 2rem);
      }

      &_links {
         list-style: none;
         display: flex;
         flex-direction: column;

         @media (max-width: 768px) {
            font-size: 1.4rem;
         }

         & li {
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 1.6rem;
         }

         .nav_link {
            width: 100%;
            display: block;
            padding: 2rem;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:hover {
               background-color: rgba(255, 255, 255, 0.05);
            }

            .nav_icon {
               display: flex;
               align-items: center;
               justify-content: center;
               font-size: 1.8rem;
            }
         }
      }

      .nav_btn {
         padding: 0.8rem 3rem;
         margin: 2rem 0;
         font-size: 1.5rem;
         border-radius: 0.7rem;
         justify-self: center;
      }

      .user_info {
         padding: 2rem;
         margin-bottom: 0;
         border-bottom: 1px solid rgba(255, 255, 255, 0.05);
         display: flex;
         flex-direction: column;
         align-items: center;
         text-align: center;
         background-color: #151515;

         .avatar_container {
            margin-bottom: 1rem;
         }

         .user_name {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 0.4rem;
         }

         .user_email {
            font-size: 1.4rem;
            color: var(--text-secondary);
            opacity: 0.8;
         }
      }

      .user_menu {
         display: flex;
         flex-direction: column;
         width: 100%;

         &_item {
            display: flex;
            align-items: center;
            gap: 1.2rem;
            padding: 2rem;
            font-size: 1.5rem;
            cursor: pointer;
            transition: background-color 0.2s ease;

            &:first-child {
               border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }

            &:last-child {
               color: #ff5252;
            }

            &:hover {
               background-color: rgba(255, 255, 255, 0.05);
            }

            svg {
               font-size: 1.8rem;
            }
         }
      }
   }
}
