.preview_card {
   display: flex;
   background-color: #171717;
   border-radius: 1rem;
   overflow: hidden;
   max-width: 40rem;

   @media (max-width: 768px) {
      max-width: 100%;
   }

   .image_container {
      position: relative;
      width: 10rem;
      height: 10rem;
      flex-shrink: 0;
   }

   .image {
      object-fit: cover;
   }

   .content {
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
   }

   .title {
      font-size: 1.6rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #fff;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
   }

   .progress_container {
      display: flex;
      align-items: center;
      gap: 1rem;
   }

   .progress_bar {
      flex: 1;
      height: 4px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      overflow: hidden;
      position: relative;
   }

   .progress_fill {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: var(--background-gradient);
      border-radius: 2px;
   }
}
