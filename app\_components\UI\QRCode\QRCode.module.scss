.qr_container {
   position: relative;
   display: flex;
   justify-content: center;
   align-items: center;
   background-color: white;
}

.qr_code {
   width: 100%;
   height: 100%;
}

.logo_container {
   position: absolute;
   background-color: black;
   border: 2px solid white;
   border-radius: 50%;
   padding: 1rem;
   display: flex;
   justify-content: center;
   align-items: center;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo {
   width: 100%;
   height: 100%;
   object-fit: contain;
}

.qr_placeholder {
   background-color: #f0f0f0;
   border-radius: 12px;
   animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
   0% {
      opacity: 0.6;
   }
   50% {
      opacity: 0.8;
   }
   100% {
      opacity: 0.6;
   }
}
