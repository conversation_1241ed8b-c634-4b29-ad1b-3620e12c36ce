.event {
   display: flex;
   display: grid;
   grid-template-columns: 30% 1fr;
   gap: 4rem;
   margin: 0 6rem;

   width: 1140px;
   margin: 0 auto;
   grid-template-columns:35% 1fr;

   &_img {
      position: relative;
      height: 55rem;
      overflow: hidden;
      border-radius: 2rem;
      margin-bottom: 2rem;

      img {
         object-fit: cover;
         transition: transform 0.3s;
      }
   }

   &_content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      padding: 2rem;
      font-size: 1.6rem;

      .back {
         display: flex;
         align-items: center;
         gap: 1rem;
         font-size: 1.4rem;

         &:hover {
            text-decoration: underline;

            span {
               transform: translateX(-0.3rem);
            }
         }

         span {
            padding: 1rem;
            border-radius: 50%;
            background-color: #1b1b1b;
            display: flex;
            transition: all 0.2s;
         }

         svg {
            font-size: 1.6rem;
         }
      }

      p {
         display: flex;
         align-items: center;
         gap: 1rem;
         line-height: 1.2;

         svg {
            font-size: 2.5rem;
         }
      }

      hr {
         height: 1px;
         border: none;
         color: #181818;
         background-color: #181818;
      }

      .title {
         font-size: 4rem;
         font-weight: 600;
         line-height: 1.2;
      }

      .price {
         margin-top: 2rem;
         font-size: 2.5rem;
         font-weight: 800;
         letter-spacing: 1px;
      }

      .about {
         display: flex;
         flex-direction: column;
         gap: 1.5rem;

         &_title {
            font-size: 2.5rem;
            font-weight: 600;
            line-height: 1.2;
         }

         &_description {
            font-size: 1.6rem;
            line-height: 1.5;
         }
      }
   }
}
