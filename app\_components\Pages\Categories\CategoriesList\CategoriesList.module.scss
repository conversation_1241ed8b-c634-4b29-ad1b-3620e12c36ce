// .categories {
//    display: flex;
//    flex-wrap: wrap;
//    gap: 1.2rem 1rem;
//    margin: 1.5rem 0;
//    // margin: 2rem 0;

//    .category {
//       padding: 1rem 2rem;
//       background-color: #1b1b1b;
//       border-radius: 5rem;

//       &.active {
//          background-color: #fff;
//          color: #1b1b1b;
//       }

//       h3 {
//          font-size: 1.4rem;
//          font-weight: 500;
//       }
//    }
// }

// .title {
//    margin-top: 1rem;
// }

.categories_container {
   background: #171717;
   border-radius: 0.8rem;
   border: 1px solid #222;
   // padding: 2rem 2.5rem 1.5rem 2.5rem;
   padding: 2rem;
   margin-bottom: 2.5rem;
   box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.12);
}

.filter_title {
   color: #fff;
   font-size: 1.6rem;
   font-weight: 600;
   margin-bottom: 1.2rem;
   letter-spacing: 0.01em;
}

.categories_grid {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(18rem, 1fr));
   gap: 1.4rem;
}

.category_btn {
   display: flex;
   align-items: center;
   gap: 0.7rem;
   padding: 1.2rem 1.8rem;
   background: #030303;
   border: 1px solid #1e1e1e;
   border-radius: 0.7rem;
   color: #fff;
   font-size: 1.4rem;
   font-weight: 500;
   text-decoration: none;
   transition:
      background 0.18s,
      color 0.18s;
   position: relative;
   outline: none;
   box-shadow: none;
   cursor: pointer;
   min-width: 120px;
}

.category_btn:hover:not(.active) {
   background: #141414;
}

.category_btn.active {
   background-color: white;
   color: #000;
   font-weight: 700;
}

.all_category {
   color: #000;
   font-weight: 700;
}

.all_category:not(.active) {
   background: #111;
   color: #fff;
   font-weight: 500;
}

.category_label {
   flex: 1;
   font-size: 1.3rem;
   font-weight: 500;
   letter-spacing: 0.01em;
}

.category_count {
   display: inline-flex;
   display: flex;
   align-items: center;
   justify-content: center;
   min-width: 2rem;
   height: 2rem;
   background: #232323;
   color: #fff;
   border-radius: 0.8rem;
   font-size: 1.1rem;
   font-weight: 600;
   margin-left: 0.2rem;
   box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
   transition:
      background 0.18s,
      color 0.18s;
}

.category_btn.active .category_count,
.all_category.active .category_count {
   background: #000;
   color: #fff;
}

.description {
   margin-top: 0.5rem;
   margin-bottom: 0.5rem;
   color: #888;
   font-size: 1.5rem;
   line-height: 1.6;
}
