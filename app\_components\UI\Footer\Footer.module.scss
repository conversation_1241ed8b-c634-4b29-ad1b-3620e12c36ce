.footer {
   margin-top: auto;
   display: flex;
   flex-direction: column;
   gap: 5rem;
   padding: 6rem;
   transition: all 0.3s ease;
   max-width: 100%;
   position: relative;
   background: linear-gradient(
      135deg,
      #0a0a0a 0%,
      #1a1a1a 25%,
      #2a2a2a 50%,
      #1a1a1a 75%,
      #0a0a0a 100%
   );
   box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);

   @media (max-width: 1024px) {
      padding: 4rem 3rem;
   }

   @media (max-width: 425px) {
      padding: 4rem 2rem;
   }

   .social .social_link {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;

      &:hover,
      &:focus {
         background: rgba(255, 255, 255, 0.2);
         transform: translateY(-3px);
         box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }
   }

   &_logo {
      position: relative;
      height: 10rem;
      width: 20rem;

      @media (max-width: 1024px) {
         height: 8rem;
         width: 16rem;
      }
   }

   &_top {
      display: flex;

      @media (max-width: 425px) {
         flex-direction: column;
         align-items: center;
         gap: 2rem;
      }

      .footer_btns {
         margin-left: auto;
         display: flex;
         flex-direction: column;
         gap: 1rem;
         transition: all 0.3s ease;

         @media (max-width: 425px) {
            margin-left: 0;
         }

         button {
            min-width: 120px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;

            &:hover,
            &:focus {
               transform: translateY(-2px);
               box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
               outline: none;
            }
         }
      }
   }

   &_bottom {
      display: flex;
      justify-content: space-between;

      @media (max-width: 768px) {
         flex-direction: column-reverse;
         gap: 2rem;
         align-items: center;
      }

      .copyright {
         font-size: 1.4rem;
         color: #b4b4b4;
         transition: color 0.3s ease;

         &:hover {
            color: #ffffff;
         }
      }

      .social {
         display: flex;
         align-items: center;
         font-size: 1.4rem;
         gap: 1.5rem;
         transition: color 0.3s ease;

         &_links {
            display: flex;
            gap: 1.5rem;
         }

         &_link {
            display: flex;
            font-size: 2rem;
            padding: 0.8rem;
            background-color: #1a1a1a;
            color: #e6e6e6;
            border-radius: 10rem;
            transition: transform 0.3s ease, background-color 0.3s ease;

            &:hover,
            &:focus {
               transform: translateY(-3px);
               background-color: #2a2a2a;
               outline: none;
            }
         }
      }
   }
}
