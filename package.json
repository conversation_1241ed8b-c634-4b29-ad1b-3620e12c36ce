{"name": "pimpim", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "email": "email dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/firebase-adapter": "^2.8.0", "@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.3.0", "@react-email/components": "^0.0.36", "@uidotdev/usehooks": "^2.4.1", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.3.1", "firebase-admin": "^12.7.0", "html-to-image": "^1.11.13", "motion": "^11.18.0", "next": "^15.2.4", "next-auth": "^5.0.0-beta.25", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-qr-code": "^2.0.15", "resend": "^4.3.0", "sonner": "^2.0.3", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "eslint": "^9", "eslint-config-next": "15.2.1", "next-sitemap": "^4.2.3", "react-email": "^4.0.7", "sass": "^1.83.1", "typescript": "^5"}, "overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}}