.container {
   grid-column: span 2;
   position: relative;

   .title {
      display: flex;
      justify-content: space-between;

      a {
         display: flex;
         align-items: center;
         gap: 0.5rem;
         font-size: 1.5rem;

         svg {
            font-size: 2rem;
         }
      }
   }

   .left_handle,
   .right_handle {
      background-color: white;
      color: black;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 5rem;
      cursor: pointer;
      padding: 0 1rem;
      z-index: 10;
      position: absolute;
      top: 55%;
      transform: translateY(-50%);
      height: 5rem;
      width: 5rem;
      border-radius: 20rem;

      @media (max-width: 1024px) {
         font-size: 4rem;
         height: 4rem;
         width: 4rem;
      }
   }

   .left_handle {
      left: -2.5rem;

      @media (max-width: 1024px) {
         left: -1.5rem;
      }
   }
   .right_handle {
      right: -2.5rem;

      @media (max-width: 1024px) {
         right: -1.5rem;
      }
   }
}

.categories {
   display: flex;
   margin-top: 1.5rem;
   gap: 3rem;
   overflow-x: scroll;
   scroll-behavior: smooth;

   /* Hide scrollbar for Chrome, Safari and Opera */
   &::-webkit-scrollbar {
      display: none;
   }

   /* Hide scrollbar for IE, Edge and Firefox */
   & {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
   }

   .category {
      &_img {
         position: relative;
         height: 15rem;
         width: 30rem;
         border-radius: 1rem 1rem 0 0;
         overflow: hidden;

         @media (max-width: 1024px) {
            height: 12rem;
            width: 25rem;
         }

         @media (max-width: 768px) {
            height: 10rem;
            width: 20rem;
         }

         img {
            object-fit: cover;
            height: 100%;
            width: 100%;
         }
      }

      &_name {
         font-size: 1.6rem;
         font-weight: 600;
         text-align: center;
         padding: 1.2rem;
         background: #1a1a1a;
         border-radius: 0 0 1rem 1rem;
      }
   }
}
