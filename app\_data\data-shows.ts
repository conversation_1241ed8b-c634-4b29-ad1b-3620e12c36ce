// import { Series } from "./types";

import { Series } from "../_lib/firebase/types";

export const shows: Series[] = [
   {
      id: "1",
      category: "show",
      title: "The Flash",
      year: "2021",
      description:
         "The Flash is a superhero series about a forensic scientist who gains super speed after a freak accident and uses his powers to fight crime.",
      rating: 9.5,
      genres: ["Action", "Adventure", "Comedy"],
      credits: "Cast: Actor 1, Actor 2, Actor 3",
      trailerLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
      poster: "/images/movie1.webp",
      slug: "the-flash",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Pilot",
            description: "Pilot episode.",
            rating: 9.5,
            episodeLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
            viewCount: 1000,
         },
         {
            episodeNumber: 2,
            title: "Fastest Man Alive",
            description: "<PERSON> learns to control his speed.",
            rating: 9.2,
            episodeLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
            viewCount: 980,
         },
         {
            episodeNumber: 3,
            title: "Things You Can't Outrun",
            description: "A dangerous foe returns.",
            rating: 9.1,
            episodeLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
            viewCount: 950,
         },
         {
            episodeNumber: 4,
            title: "Going Rogue",
            description: "Barry faces a new enemy.",
            rating: 9.0,
            episodeLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
            viewCount: 940,
         },
         {
            episodeNumber: 5,
            title: "Plastique",
            description: "Barry tracks down a dangerous meta-human.",
            rating: 8.8,
            episodeLink: "https://www.youtube.com/watch?v=cuATJ5Mlrok",
            viewCount: 920,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "2",
      category: "show",
      title: "The Big Bang Theory",
      year: "2007",
      description:
         "The Big Bang Theory is a comedy series about a group of socially awkward scientists and their neighbor, a waitress and aspiring actress.",
      rating: 8.4,
      genres: ["Comedy"],
      credits: "Cast: Johnny Galecki, Jim Parsons, Kaley Cuoco",
      trailerLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
      poster: "/images/movie2.webp",
      slug: "the-big-bang-theory",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Pilot",
            description: "Pilot episode.",
            rating: 8.4,
            episodeLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
            viewCount: 1000,
         },
         {
            episodeNumber: 2,
            title: "The Big Bran Hypothesis",
            description: "Leonard offers to do something nice for Penny.",
            rating: 8.2,
            episodeLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
            viewCount: 980,
         },
         {
            episodeNumber: 3,
            title: "The Fuzzy Boots Corollary",
            description: "Leonard asks Penny out on a date.",
            rating: 8.3,
            episodeLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
            viewCount: 970,
         },
         {
            episodeNumber: 4,
            title: "The Luminous Fish Effect",
            description: "Sheldon is fired from his job.",
            rating: 8.1,
            episodeLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
            viewCount: 960,
         },
         {
            episodeNumber: 5,
            title: "The Hamburger Postulate",
            description: "Leslie invites Leonard to her apartment.",
            rating: 8.0,
            episodeLink: "https://www.youtube.com/watch?v=WBb3fojgW0Q",
            viewCount: 950,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "3",
      category: "show",
      title: "Stranger Things",
      year: "2016",
      description:
         "Stranger Things is a sci-fi horror series about a group of kids who band together to investigate supernatural events in their town.",
      rating: 8.2,
      genres: ["Horror", "Science Fiction"],
      credits: "Cast: Winona Ryder, David Harbour, Finn Wolfhard",
      trailerLink: "https://www.youtube.com/watch?v=XWxyRG_tckY",
      poster: "/images/movie1.webp",
      slug: "stranger-things",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "The Vanishing of Will Byers",
            description: "Will goes missing.",
            rating: 8.2,
            episodeLink: "https://www.youtube.com/watch?v=R1ZXOOLMJ8s",
            viewCount: 1000,
         },
         {
            episodeNumber: 2,
            title: "The Weirdo on Maple Street",
            description: "Lucas and Dustin meet Eleven.",
            rating: 8.5,
            episodeLink: "https://www.youtube.com/watch?v=R1ZXOOLMJ8s",
            viewCount: 990,
         },
         {
            episodeNumber: 3,
            title: "Holly, Jolly",
            description: "Joyce finds a strange clue.",
            rating: 8.4,
            episodeLink: "https://www.youtube.com/watch?v=R1ZXOOLMJ8s",
            viewCount: 980,
         },
         {
            episodeNumber: 4,
            title: "The Body",
            description: "Hopper uncovers a lab secret.",
            rating: 8.3,
            episodeLink: "https://www.youtube.com/watch?v=R1ZXOOLMJ8s",
            viewCount: 970,
         },
         {
            episodeNumber: 5,
            title: "The Flea and the Acrobat",
            description: "The boys learn about a dimension.",
            rating: 8.1,
            episodeLink: "https://www.youtube.com/watch?v=R1ZXOOLMJ8s",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "4",
      category: "show",
      title: "The Office (US)",
      year: "2005",
      description:
         "The Office is a mockumentary-style sitcom about the employees of a paper company in Scranton, Pennsylvania.",
      rating: 8.8,
      genres: ["Comedy"],
      credits: "Cast: Steve Carell, John Krasinski, Rainn Wilson",
      trailerLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
      poster: "/images/movie2.webp",
      slug: "the-office-us",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Pilot",
            description: "Pilot episode.",
            rating: 8.8,
            episodeLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
            viewCount: 1000,
         },
         {
            episodeNumber: 2,
            title: "Diversity Day",
            description: "Michael's seminar goes awry.",
            rating: 8.6,
            episodeLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
            viewCount: 990,
         },
         {
            episodeNumber: 3,
            title: "Health Care",
            description: "Michael delegates a task.",
            rating: 8.5,
            episodeLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
            viewCount: 980,
         },
         {
            episodeNumber: 4,
            title: "The Alliance",
            description: "Jim pulls a prank on Dwight.",
            rating: 8.4,
            episodeLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
            viewCount: 970,
         },
         {
            episodeNumber: 5,
            title: "Basketball",
            description: "Office vs. warehouse game.",
            rating: 8.3,
            episodeLink: "https://www.youtube.com/watch?v=7QvV3y7qf0Q",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "5",
      category: "show",
      title: "Game of Thrones",
      year: "2011",
      description:
         "Game of Thrones is a fantasy drama series about the battle for the Iron Throne in the fictional continents of Westeros and Essos.",
      rating: 9.2,
      genres: ["Drama", "Fantasy"],
      credits: "Cast: Peter Dinklage, Emilia Clarke, Kit Harington",
      trailerLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
      poster: "/images/movie1.webp",
      slug: "game-of-thrones",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Winter is Coming",
            description: "Pilot episode.",
            rating: 9.2,
            episodeLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
            viewCount: 1000,
         },
         {
            episodeNumber: 2,
            title: "The Kingsroad",
            description: "The Stark family begins to split.",
            rating: 9.1,
            episodeLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
            viewCount: 990,
         },
         {
            episodeNumber: 3,
            title: "Lord Snow",
            description: "Jon struggles with his new life.",
            rating: 9.0,
            episodeLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
            viewCount: 980,
         },
         {
            episodeNumber: 4,
            title: "Cripples, Bastards, and Broken Things",
            description: "Ned probes Jon Arryn's death.",
            rating: 8.9,
            episodeLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
            viewCount: 970,
         },
         {
            episodeNumber: 5,
            title: "The Wolf and the Lion",
            description: "Tyrion is arrested.",
            rating: 8.8,
            episodeLink: "https://www.youtube.com/watch?v=KPLWWIOQSYU",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "6",
      category: "show",
      title: "Narcos",
      year: "2015",
      description:
         "Narcos is a crime drama series about the rise and fall of Colombian drug lord Pablo Escobar and the Medell n cartel.",
      rating: 8.8,
      genres: ["Crime", "Drama"],
      credits: "Cast: Wagner Moura, Boyd Holbrook, Pedro Pascal",
      trailerLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
      poster: "/images/movie2.webp",
      slug: "narcos",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Descenso",
            description: "Pablo Escobar rises to power.",
            rating: 8.8,
            episodeLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
            viewCount: 1000,
         },
         {
            episodeNumber: 1,
            title: "The Sword of Simón Bolívar",
            description: "Escobar's empire grows.",
            rating: 8.7,
            episodeLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
            viewCount: 990,
         },
         {
            episodeNumber: 1,
            title: "The Men of Always",
            description: "Escobar moves into politics.",
            rating: 8.6,
            episodeLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
            viewCount: 980,
         },
         {
            episodeNumber: 1,
            title: "The Palace in Flames",
            description: "The violence escalates.",
            rating: 8.5,
            episodeLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
            viewCount: 970,
         },
         {
            episodeNumber: 1,
            title: "There Will Be a Future",
            description: "Escobar plans for the future.",
            rating: 8.4,
            episodeLink: "https://www.youtube.com/watch?v=3EsrK7y3s2Q",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "7",
      category: "show",
      title: "Breaking Bad",
      year: "2008",
      description:
         "Breaking Bad is a crime drama series about a high school chemistry teacher turned methamphetamine manufacturer.",
      rating: 9.4,
      genres: ["Crime", "Drama"],
      credits: "Cast: Bryan Cranston, Aaron Paul, Anna Gunn",
      trailerLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
      poster: "/images/movie1.webp",
      slug: "breaking-bad",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "Pilot",
            description: "Pilot episode.",
            rating: 9.4,
            episodeLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
            viewCount: 1000,
         },
         {
            episodeNumber: 1,
            title: "Cat's in the Bag...",
            description: "Walt and Jesse face a problem.",
            rating: 9.3,
            episodeLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
            viewCount: 990,
         },
         {
            episodeNumber: 1,
            title: "...And the Bag's in the River",
            description: "Walt deals with a dilemma.",
            rating: 9.2,
            episodeLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
            viewCount: 980,
         },
         {
            episodeNumber: 1,
            title: "Cancer Man",
            description: "Walt faces his diagnosis.",
            rating: 9.1,
            episodeLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
            viewCount: 970,
         },
         {
            episodeNumber: 1,
            title: "Gray Matter",
            description: "Walt's past resurfaces.",
            rating: 9.0,
            episodeLink: "https://www.youtube.com/watch?v=HhesaQXLuRY",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
   {
      id: "8",
      category: "show",
      title: "The Witcher",
      year: "2019",
      description:
         "The Witcher is a fantasy drama series based on the popular video game and book series.",
      rating: 8.4,
      genres: ["Drama", "Fantasy"],
      credits: "Cast: Henry Cavill, Freya Allan, Anya Chalotra",
      trailerLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
      poster: "/images/movie2.webp",
      slug: "the-witcher",
      createdAt: new Date("2022-01-01T12:00:00.000Z"),
      episodes: [
         {
            episodeNumber: 1,
            title: "The End's Beginning",
            description: "Pilot episode.",
            rating: 8.4,
            episodeLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
            viewCount: 1000,
         },
         {
            episodeNumber: 1,
            title: "Four Marks",
            description: "Yennefer's origin begins.",
            rating: 8.3,
            episodeLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
            viewCount: 990,
         },
         {
            episodeNumber: 1,
            title: "Betrayer Moon",
            description: "Geralt hunts a monster.",
            rating: 8.2,
            episodeLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
            viewCount: 980,
         },
         {
            episodeNumber: 1,
            title: "Of Banquets, Bastards and Burials",
            description: "A royal affair unfolds.",
            rating: 8.1,
            episodeLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
            viewCount: 970,
         },
         {
            episodeNumber: 1,
            title: "Bottled Appetites",
            description: "A genie causes trouble.",
            rating: 8.0,
            episodeLink: "https://youtu.be/ndl1W4ltcmg?si=u04U1sKWKKDa3tZM",
            viewCount: 960,
         },
      ],
      viewCount: 1000,
   },
];
