.container {
   margin: 0;
   padding: 0;
   display: flex;
   justify-content: center;
   align-items: center;
   background: #151515;
   border-radius: 1rem;
   overflow: hidden;
   position: relative;
   height: 100%;
   height: 50rem;
   width: 100%;

   @media (max-width: 425px) {
      height: 40rem;
   }

   &:hover {
      .next,
      .prev {
         opacity: 1;
      }
   }

   .image_wrapper {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      img {
         object-fit: cover;
         filter: brightness(90%);
      }
   }

   .title {
      position: relative;
      left: 0;
      bottom: 3rem;
      width: 100%;
      // text-align: center;
      font-size: 3rem;
      margin: 0 8rem;
      align-self: flex-end;
      margin-bottom: 2rem;
      z-index: 5;
      line-height: 1.4;

      @media (max-width: 1024px) {
         margin: 0 4rem;
         margin-bottom: 2rem;
      }

      @media (max-width: 768px) {
         margin: 0 2rem;
         margin-bottom: 2rem;
         font-size: 2.5rem;
      }

      @media (max-width: 525px) {
         margin: 0 2rem;
         margin-bottom: 2rem;
         font-size: 2.2rem;
         line-height: 1.2;
      }

      p span {
         display: inline;
         background-image: linear-gradient(to right, red);
         background-image: red;
         background-image: var(--background-gradient);
         background-size: 0% 6px;
         // background-size: 0% 1.5rem;
         background-position: left bottom;
         background-position: 0 90%;
         background-repeat: no-repeat;
         transition-property: background-size;
         transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
         transition-duration: 500ms;
      }

      &:hover p span {
         background-size: 100% 1.5rem;
         background-size: 100% 0.6rem;
      }

      // &_container {
      //    line-height: 1.4;
      //    max-height: calc(2 * 1em * 1.4);
      // }
   }

   .category {
      font-size: 1.2rem;
      position: absolute;
      width: 100%;
      display: flex;
      // justify-content: center;
      padding: 0 8rem;
      bottom: 16rem;
      z-index: 5;

      @media (max-width: 1024px) {
         padding: 0 4rem;
      }

      @media (max-width: 768px) {
         padding: 0 2rem;
         bottom: 14rem;
      }

      @media (max-width: 525px) {
         bottom: 12rem;
      }

      &_text {
         font-size: 1.4rem;
         font-weight: 500;
         background-color: #1b1b1b;
         padding: 1rem 2rem;
         padding: 1.2rem 2.5rem;
         padding: 1rem 2.5rem;
         padding: 0.7rem 2rem;
         border: 2px solid #b9b9b9;
         border-radius: 10rem;
         text-transform: capitalize;
      }
   }

   .line_clamp {
      display: -webkit-box;
      line-clamp: 3;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
   }

   .next,
   .prev {
      position: absolute;
      top: 50%;
      width: 50px;
      height: 50px;

      width: 4rem;
      height: 8rem;
      display: flex;
      justify-content: center;
      align-items: center;

      background: white;
      color: black;
      // background: #1b1b1b;
      // color: white;
      background: rgba(0, 0, 0, 0.6);
      color: white;

      // border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      user-select: none;
      transform: translateY(-50%);
      z-index: 10;
      opacity: 0;
      transition: 0.3s;

      @media (max-width: 1024px) {
         width: 3rem;
         height: 6rem;
         opacity: 1;
      }
   }

   .next {
      right: 2rem;
      right: 0rem;
   }

   .prev {
      left: 2rem;
      left: 0rem;
      transform: translateY(-50%) scaleX(-1);
   }

   .dots {
      display: flex;
      z-index: 5;
      position: absolute;
      bottom: 1.5rem;
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(16px) saturate(180%);
      background-color: rgba(0, 0, 0, 0.25);
      padding: 1rem;
      border-radius: 20rem;
   }

   .dot {
      width: 1.2rem;
      height: 1.2rem;
      margin: 0 5px;
      border-radius: 50%;
      background: #333;
      cursor: pointer;
      transition: background 0.3s;
   }

   .dot.active {
      background: #888888;
   }

   .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
         to bottom,
         rgba(255, 255, 255, 0) 0%,
         rgba(0, 0, 0, 0.5) 70%,
         rgba(0, 0, 0, 0.7) 100%
      );
      background: linear-gradient(
         to bottom,
         rgba(255, 255, 255, 0) 0%,
         rgba(255, 255, 255, 0) 50%,
         rgba(0, 0, 0, 0.5) 70%,
         rgba(0, 0, 0, 0.7) 100%
      );
      z-index: 1;

      // &:hover ~ .title p {
      //    text-decoration: underline;
      // }
   }
}
