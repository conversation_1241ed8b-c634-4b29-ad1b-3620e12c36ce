.image_input_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 1rem;

  .hidden_input {
    display: none;
  }

  .select_button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    background-color: #1a1a1a;
    color: white;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 1.2rem 2rem;
    font-size: 1.4rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;

    &:hover {
      background-color: #252525;
      border-color: #444;
    }

    &:active {
      transform: translateY(1px);
    }

    .icon {
      font-size: 1.6rem;
    }
  }

  .file_info {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background-color: #0f0f0f;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-top: 0.5rem;

    .file_icon {
      color: #666;
      font-size: 1.6rem;
      flex-shrink: 0;
    }

    .file_name {
      font-size: 1.3rem;
      color: #bbb;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .error_message {
    color: #ff4d4f;
    font-size: 1.2rem;
    margin-top: 0.4rem;
  }
}
