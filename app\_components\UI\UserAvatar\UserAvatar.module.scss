.avatar {
   border-radius: 50%;
   display: flex;
   align-items: center;
   justify-content: center;
   font-weight: 600;
   color: #fff;
   cursor: pointer;
   transition: all 0.2s ease;
   background: var(--background-gradient);
   background-size: 200% 100%;
   overflow: hidden;
   position: relative;

   &.small {
      width: 35px;
      height: 35px;
      font-size: 1.4rem;
   }

   &.medium {
      width: 45px;
      height: 45px;
      font-size: 1.6rem;
   }

   &.large {
      width: 80px;
      height: 80px;
      font-size: 2.4rem;
   }

   &.xlarge {
      width: 120px;
      height: 120px;
      font-size: 3rem;
   }

   &:hover {
      transform: scale(1.05);
   }

   &.dark_bg {
      background: #111;
      border: 1px solid rgba(255, 255, 255, 0.1);
   }

   .image {
      width: 100%;
      height: 100%;
      object-fit: cover;
   }
}
