// Dialog styles
.dialog_overlay {
   position: fixed;
   inset: 0;
   background-color: rgba(0, 0, 0, 0.5);
   // backdrop-filter: blur(4px);
   display: flex;
   align-items: center;
   justify-content: flex-end;
   z-index: 1000;
}

.dialog_panel {
   position: relative;
   background-color: #080808;
   height: 100%;
   width: 100%;
   max-width: 450px;
   box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
   display: flex;
   flex-direction: column;

   @media (max-width: 768px) {
      max-width: 100%;
   }
}

.dialog_header {
   padding: 1.5rem 2rem;
   border-bottom: 1px solid #222;
   display: flex;
   justify-content: space-between;
   align-items: center;

   h2 {
      font-size: 1.8rem;
      font-weight: 600;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80%;
   }

   .header_buttons {
      display: flex;
      align-items: center;
      gap: 1rem;
   }

   .action_button {
      background: transparent;
      border: none;
      color: var(--text-secondary);
      font-size: 2.4rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;

      &:hover {
         color: var(--text-primary);
      }

      &:disabled {
         opacity: 0.5;
         cursor: not-allowed;
      }
   }

   .refresh_button {
      &.refreshing {
         animation: spin 1s linear infinite;
      }
   }
}

.dialog_content {
   flex: 1;
   overflow-y: auto;
   padding: 0;
   display: flex;
   flex-direction: column;
}

// Comment input styles
.comment_input_container {
   position: sticky;
   bottom: 0;
   left: 0;
   right: 0;
   padding: 1.5rem;
   background-color: #080808;
   border-top: 1px solid #222;
   z-index: 10;
   box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.2);
}

// Comment skeleton styles
.comment_skeleton {
   background-color: #181818;
   border-radius: 0.8rem;
   padding: 1.5rem;
}

.comment_form {
   width: 100%;

   .comment_textarea {
      width: 100%;
      background-color: #181818;
      border: none;
      border-radius: 0.8rem;
      padding: 1.5rem;
      color: var(--text-primary);
      font-size: 1.6rem;
      min-height: 8rem;
      margin-bottom: 1.5rem;
      resize: vertical;

      &:focus {
         outline: none;
         box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
      }

      &.disabled {
         opacity: 0.6;
         cursor: not-allowed;
         background-color: #1a1a1a;
         color: var(--text-secondary);
      }
   }

   .form_actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;
   }
}

// Comments list styles
.comments_container {
   flex: 1;
   padding: 1.5rem;
   overflow-y: auto;
}

.comments_list {
   display: flex;
   flex-direction: column;
   gap: 2rem;
}

.comments_skeleton {
   display: flex;
   flex-direction: column;
   gap: 1.5rem;
}

// Load more button styles
.load_more_container {
   display: flex;
   justify-content: center;
   margin-bottom: 1rem;
}

.load_more_button {
   background: transparent;
   border: 1px solid rgba(255, 255, 255, 0.1);
   color: var(--text-secondary);
   padding: 1rem 2rem;
   border-radius: 3rem;
   font-size: 1.4rem;
   cursor: pointer;
   transition: all 0.2s ease;
   min-width: 19.5rem;
   min-height: 4.5rem;
   position: relative;
   overflow: hidden;

   &:hover {
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--text-primary);
   }

   &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
   }

   &.loading {
      background-color: rgba(255, 255, 255, 0.03);
   }
}

// Comment item styles
.comment_item {
   display: flex;
   gap: 2.2rem;

   &.reply {
      margin-top: 1.5rem;
      margin-left: 1rem;
      padding-left: 1rem;
      border-left: 2px solid #333;
   }
}

.comment_avatar {
   flex-shrink: 0;
   width: 4rem;
   height: 4rem;

   .reply & {
      width: 4.2rem;
      height: 4.2rem;
   }
}

.comment_content {
   flex: 1;
   display: flex;
   flex-direction: column;
   gap: 0.8rem;
}

.comment_header {
   display: flex;
   align-items: center;
   gap: 1rem;

   .comment_author {
      font-weight: 600;
      font-size: 1.5rem;
      color: var(--text-primary);
   }

   .comment_time {
      font-size: 1.2rem;
      color: var(--text-secondary);
   }
}

.comment_text {
   font-size: 1.4rem;
   line-height: 1.5;
   color: var(--text-secondary);
   word-break: break-word;
}

.comment_actions {
   display: flex;
   gap: 1rem;
   margin-top: 0.5rem;

   .action_button {
      background: transparent;
      border: none;
      color: var(--text-secondary);
      font-size: 1.4rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0;
      transition: color 0.2s;

      &:hover {
         color: var(--text-primary);
      }

      &.liked {
         color: #3a86ff;
      }

      &.delete_button {
         color: #ff5252;

         &:hover {
            color: #ff1919;
         }

         svg {
            font-size: 2rem;
         }
      }

      &.disabled {
         opacity: 0.5;
         cursor: not-allowed;

         &:hover {
            color: var(--text-secondary);
         }
      }

      &.view_replies_button {
         font-size: 1.3rem;
         color: var(--text-secondary);
         background-color: rgba(255, 255, 255, 0.03);
         padding: 0.5rem 1rem;
         border-radius: 1.5rem;
         transition: all 0.2s ease;

         &:hover {
            background-color: rgba(255, 255, 255, 0.08);
         }

         .replies_count {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 2rem;
            height: 2rem;
            padding: 0 0.5rem;
            margin-left: 0.5rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            font-size: 1.2rem;
            font-weight: 500;
         }
      }

      svg {
         font-size: 1.8rem;
      }
   }
}

.replies {
   display: flex;
   flex-direction: column;
   gap: 1.5rem;
   margin-top: 1rem;
}

.replies_container {
   display: flex;
   flex-direction: column;
   gap: 1.5rem;
   margin-top: 0.5rem;
   padding-top: 0.5rem;
}

.reply_input {
   margin-top: 1rem;
}

// Loading, error, and empty states
.loading_container,
.error_container,
.empty_container {
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   padding: 4rem 2rem;
   text-align: center;
   color: var(--text-secondary);
   font-size: 1.5rem;

   p {
      margin-bottom: 1.5rem;
   }
}

.spinner {
   font-size: 3rem;
   margin-bottom: 1.5rem;
   animation: spin 1s linear infinite;
}

@keyframes spin {
   from {
      transform: rotate(0deg);
   }
   to {
      transform: rotate(360deg);
   }
}

.retry_button {
   background-color: #222;
   border: none;
   color: var(--text-primary);
   padding: 0.8rem 1.5rem;
   border-radius: 0.5rem;
   font-size: 1.4rem;
   cursor: pointer;
   transition: background-color 0.2s;

   &:hover {
      background-color: #333;
   }
}

// Comment button for Post component
.comment_button {
   justify-self: center;
   display: flex;
   justify-content: center;
   align-items: center;
   gap: 0.8rem;
   border-radius: 30px;
   padding: 1.2rem 3rem;
   background-color: #1a1a1a;
   border: 1px solid #2a2a2a;
   color: var(--text-primary);
   font-size: 1.5rem;
   font-weight: 500;
   width: auto;
   transition: all 0.25s ease;
   position: relative;
   cursor: pointer;
   overflow: hidden;

   &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
         to right,
         transparent,
         rgba(255, 255, 255, 0.05),
         transparent
      );
      animation: shimmer 2s infinite;
      opacity: 0;
      transition: opacity 0.3s ease;
   }

   &:hover::before {
      opacity: 1;
   }

   &::after {
      content: "";
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 1px;
      background: linear-gradient(
         to right,
         transparent,
         rgba(255, 255, 255, 0.2),
         transparent
      );
      opacity: 0;
      transition: opacity 0.3s ease;
   }

   &:hover {
      background-color: #222;
      transform: translateY(-2px);

      &::after {
         opacity: 1;
      }

      svg {
         transform: translateY(2px);
      }
   }

   &:active {
      transform: translateY(0);
   }

   span {
      font-weight: 500;
   }

   svg {
      font-size: 1.8rem;
      transition: transform 0.3s ease;
      animation: pulse 2s infinite;
   }

   @keyframes pulse {
      0%,
      100% {
         transform: translateY(0);
      }
      50% {
         transform: translateY(2px);
      }
   }

   @keyframes shimmer {
      0% {
         left: -100%;
      }
      100% {
         left: 100%;
      }
   }
}
