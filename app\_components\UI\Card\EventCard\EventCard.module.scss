.card {
   display: grid;
   grid-template-columns: auto 60%;
   width: 100%;
   border-radius: 3rem;
   overflow: hidden;
   background-color: #181818;
   cursor: pointer;

   @media (max-width: 1024px) {
      grid-template-columns: 100%;
      grid-template-rows: 1fr 1fr;
   }

   &:hover {
      .card_img {
         img {
            transform: scale(1.1);
         }
      }
   }

   &_content {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      padding: 2rem;
      font-size: 1.4rem;

      .card_info {
         display: grid;
         grid-template-columns: 2rem 1fr;
         gap: 1rem;
         align-items: center;

         svg {
            font-size: 2rem;
         }
      }

      .title {
         font-size: 2.2rem;
         font-weight: 600;
         line-height: 1.2;
         max-width: 38rem;
         white-space: nowrap;
         overflow: hidden;
         text-overflow: ellipsis;
      }

      .price {
         margin-top: auto;
         font-size: 2rem;
         font-weight: 800;
         letter-spacing: 1px;
      }
   }

   &_img {
      margin-left: auto;
      position: relative;
      height: 22rem;
      margin: 1rem;
      border-radius: 2rem;
      overflow: hidden;

      img {
         object-fit: cover;
         transition: transform 0.3s;
      }
   }
}
