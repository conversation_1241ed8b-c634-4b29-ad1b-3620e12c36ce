.container {
   display: flex;
   justify-content: center;
   align-items: center;
   padding: 3rem;
   min-height: calc(80svh - 6rem);
}

.card {
   background-color: #080808;
   border-radius: 12px;
   padding: 3rem;
   width: 100%;
   max-width: 450px;
   box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

   h2 {
      font-size: 2.1rem;
      margin-bottom: 1rem;
      color: #fff;
      text-align: center;
   }
}

.subtitle {
   color: #999;
   margin-bottom: 1.5rem;
   font-size: 1.4rem;
   text-align: center;
}

.form {
   margin-top: 1.5rem;
}

.formError {
   background-color: rgba(255, 0, 0, 0.1);
   border-left: 3px solid #ff3333;
   padding: 0.75rem 1rem;
   margin-bottom: 1.5rem;
   text-align: left;
   border-radius: 4px;

   p {
      color: #ff6666;
      margin: 0;
      font-size: 1.4rem;
   }
}

.loading,
.error,
.success {
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   padding: 2rem 1rem;

   h2 {
      margin-top: 1rem;
      margin-bottom: 0.5rem;
   }

   p {
      color: #999;
      text-align: center;
      max-width: 300px;
      margin: 0 auto 1.5rem;
      font-size: 1.4rem;
   }
}

.loadingIcon {
   font-size: 3rem;
   color: #666;
   animation: spin 1.5s linear infinite;
}

.errorIcon {
   font-size: 5rem;
   color: #ff3333;
}

.successIcon {
   font-size: 5rem;
   color: #4caf50;
   background-color: rgba(76, 175, 80, 0.1);
   border-radius: 50%;
   padding: 1rem;
}

@keyframes spin {
   0% {
      transform: rotate(0deg);
   }
   100% {
      transform: rotate(360deg);
   }
}
