.edit_form_container {
   width: 100%;
   max-width: 1000px;
   margin: 0 auto;
   padding: 2rem 0;
   margin-bottom: 4rem;

   .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;

      .title {
         font-size: 2.4rem;
         font-weight: 600;
         color: var(--text-primary);
         margin: 0;
      }

      .back_button {
         display: flex;
         align-items: center;
         gap: 1rem;
         padding: 0.8rem 1.5rem;
         font-size: 1.4rem;
         background-color: transparent;
         border: 1px solid #1a1a1a;
         color: var(--text-primary);
         border-radius: 8px;
         cursor: pointer;
         transition: all 0.2s ease;

         &:hover {
            background-color: rgba(255, 255, 255, 0.05);
         }
      }
   }

   .image_editors {
      position: relative;
      width: 100%;
      margin-bottom: 8rem;

      .profile_avatar {
         position: absolute;
         bottom: -60px;
         left: 2rem;
         z-index: 100;

         :global(.image_edit) {
            justify-content: flex-start;
         }

         @media (max-width: 768px) {
            left: 50%;
            transform: translateX(-50%);

            :global(.image_edit) {
               justify-content: center;
            }
         }
      }
   }

   .form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      margin-top: 2rem;

      .form_section {
         background-color: #151515;
         border-radius: 16px;
         padding: 2rem;

         .section_title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 2rem;
            color: var(--text-primary);
         }

         .form_grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;

            @media (max-width: 768px) {
               grid-template-columns: 1fr;
            }
         }
      }

      .form_actions {
         display: flex;
         justify-content: flex-end;
         gap: 1rem;

         button {
            width: auto;
         }
      }
   }
}
